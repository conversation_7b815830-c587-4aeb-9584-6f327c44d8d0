{
    "pages": [
        // pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/index/launch",
            "style": {
                "navigationBarTitleText": "启动页",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "fade-in",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/index/index",
            "style": {
                "navigationBarTitleText": "首页",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/login/login",
            "style": {
                "navigationBarTitleText": "登录",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/task/list",
            "style": {
                "navigationBarTitleText": "取件列表",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/task/delivery-list",
            "style": {
                "navigationBarTitleText": "派件列表",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/profile/index",
            "style": {
                "navigationBarTitleText": "个人中心",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/task/detail",
            "style": {
                "navigationBarTitleText": "任务详情",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/task/delivery",
            "style": {
                "navigationBarTitleText": "去派送",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/search/index",
            "style": {
                "navigationBarTitleText": "搜索",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/message/index",
            "style": {
                "navigationBarTitleText": "消息中心",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/statistics/index",
            "style": {
                "navigationBarTitleText": "工作统计",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/freight/index",
            "style": {
                "navigationBarTitleText": "运费查询",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/exclusive/index",
            "style": {
                "navigationBarTitleText": "专属取寄",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/task/all",
            "style": {
                "navigationBarTitleText": "全部取派",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        },
        {
            "path": "pages/payment/success",
            "style": {
                "navigationBarTitleText": "支付成功",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300,
                    "safeArea": {
                        "background": "#3498db",
                        "bottom": {
                            "offset": "auto"
                        }
                    }
                }
            }
        }
    ],

    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarTitleText": "快递员端",
        "navigationBarBackgroundColor": "#3498db",
        "backgroundColor": "#3498db",
        "disableScroll": true,
        "app-plus": {
            "background": "#3498db",
            "animationType": "slide-in-right",
            "animationDuration": 300,
            "safeArea": {
                "background": "#3498db",
                "bottom": {
                    "offset": "auto"
                }
            }
        },
        "h5": {
            "animationType": "slide-in-right",
            "animationDuration": 300
        },
        "mp-weixin": {
            "animationType": "slide-in-right",
            "animationDuration": 300
        }
    },
    "tabBar": {
        "color": "#7A7E83",
        "selectedColor": "#3498db",
        "borderStyle": "black",
        "backgroundColor": "#ffffff",
        "list": [
            {
                "pagePath": "pages/index/index",
                "text": "首页"
            },
            {
                "pagePath": "pages/task/list",
                "text": "取件"
            },
            {
                "pagePath": "pages/task/delivery-list",
                "text": "派件"
            },
            {
                "pagePath": "pages/profile/index",
                "text": "我的"
            }
        ]
    },
    "uniIdRouter": {}
}
