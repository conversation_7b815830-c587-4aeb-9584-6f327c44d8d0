/**
 * 虚拟数据管理
 * 为整个应用提供统一的模拟数据
 */

// 任务状态枚举
export const TASK_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress', 
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  OVERDUE: 'overdue'
}

// 任务类型枚举
export const TASK_TYPE = {
  PICKUP: 'pickup',
  DELIVERY: 'delivery'
}

// 生成随机ID
function generateId() {
  return 'T' + Date.now() + Math.random().toString(36).substr(2, 9)
}

// 生成随机手机号
function generatePhone() {
  const prefixes = ['138', '139', '150', '151', '152', '158', '159', '188', '189']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const suffix = Math.random().toString().substr(2, 8)
  return prefix + '****' + suffix.substr(-4)
}

// 生成随机地址
function generateAddress() {
  const districts = ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '石景山区']
  const streets = ['中关村大街', '建国门外大街', '西单北大街', '王府井大街', '前门大街']
  const buildings = ['科技大厦', '商务中心', '写字楼', '住宅小区', '商业广场']
  
  const district = districts[Math.floor(Math.random() * districts.length)]
  const street = streets[Math.floor(Math.random() * streets.length)]
  const building = buildings[Math.floor(Math.random() * buildings.length)]
  const number = Math.floor(Math.random() * 999) + 1
  const room = Math.floor(Math.random() * 999) + 101
  
  return `北京市${district}${street}${number}号${building}${room}室`
}

// 生成随机姓名
function generateName() {
  const surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴']
  const names = ['先生', '女士', '小姐']
  const surname = surnames[Math.floor(Math.random() * surnames.length)]
  const name = names[Math.floor(Math.random() * names.length)]
  return surname + name
}

// 生成随机距离
function generateDistance() {
  return (Math.random() * 10 + 0.1).toFixed(1)
}

// 生成随机时间
function generateTime() {
  const hours = Math.floor(Math.random() * 12) + 8 // 8-19点
  const minutes = Math.random() > 0.5 ? '00' : '30'
  const endHour = hours + 2
  return `${hours}:${minutes}-${endHour}:${minutes}`
}

// 取件任务数据
export const pickupTasks = [
  {
    id: 'T20240917001',
    type: TASK_TYPE.PICKUP,
    status: TASK_STATUS.PENDING,
    statusText: '待取件',
    statusClass: 'status-pending',
    senderName: '李女士',
    senderPhone: '139****9999',
    pickupAddress: '北京市海淀区中关村大街1号科技大厦202室',
    pickupTime: '10:00-12:00',
    distance: '2.5',
    actionText: '去取件',
    actionClass: 'action-pickup',
    selected: false,
    weight: '2.5kg',
    itemName: '文件资料',
    paymentMethod: '寄付',
    amount: 15.00
  },
  {
    id: 'T20240917002', 
    type: TASK_TYPE.PICKUP,
    status: TASK_STATUS.PENDING,
    statusText: '待取件',
    statusClass: 'status-pending',
    senderName: '王先生',
    senderPhone: '138****8888',
    pickupAddress: '北京市朝阳区建国门外大街88号商务中心501室',
    pickupTime: '14:00-16:00',
    distance: '5.2',
    actionText: '去取件',
    actionClass: 'action-pickup',
    selected: false,
    weight: '1.2kg',
    itemName: '电子产品',
    paymentMethod: '到付',
    amount: 25.00
  },
  {
    id: 'T20240917003',
    type: TASK_TYPE.PICKUP,
    status: TASK_STATUS.COMPLETED,
    statusText: '已取件',
    statusClass: 'status-completed',
    senderName: '赵先生',
    senderPhone: '137****7777',
    pickupAddress: '北京市西城区西单北大街12号写字楼808室',
    pickupTime: '09:00-11:00',
    distance: '1.8',
    actionText: '已完成',
    actionClass: 'action-completed',
    selected: false,
    weight: '0.8kg',
    itemName: '合同文件',
    paymentMethod: '寄付',
    amount: 12.00
  }
]

// 派件任务数据
export const deliveryTasks = [
  {
    id: 'T20240917004',
    type: TASK_TYPE.DELIVERY,
    status: TASK_STATUS.PENDING,
    statusText: '待派送',
    statusClass: 'status-pending',
    customerName: '张先生',
    customerPhone: '138****8888',
    deliveryAddress: '北京市朝阳区某某街道某某小区1号楼101室',
    deliveryTime: '15:00-17:00',
    distance: '3.2',
    actionText: '去派送',
    actionClass: 'action-delivery',
    selected: false,
    trackingNumber: 'SF1234567890',
    weight: '1.5kg',
    paymentMethod: '到付',
    amount: 20.00
  },
  {
    id: 'T20240917005',
    type: TASK_TYPE.DELIVERY,
    status: TASK_STATUS.PENDING,
    statusText: '待派送',
    statusClass: 'status-pending',
    customerName: '刘女士',
    customerPhone: '139****9999',
    deliveryAddress: '北京市海淀区某某科技园A座508室',
    deliveryTime: '16:00-18:00',
    distance: '6.7',
    actionText: '去派送',
    actionClass: 'action-delivery',
    selected: false,
    trackingNumber: 'SF0987654321',
    weight: '2.8kg',
    paymentMethod: '寄付',
    amount: 0
  },
  {
    id: 'T20240917006',
    type: TASK_TYPE.DELIVERY,
    status: TASK_STATUS.COMPLETED,
    statusText: '已签收',
    statusClass: 'status-completed',
    customerName: '陈先生',
    customerPhone: '136****6666',
    deliveryAddress: '北京市东城区某某胡同3号院',
    deliveryTime: '11:00-13:00',
    distance: '4.1',
    actionText: '已完成',
    actionClass: 'action-completed',
    selected: false,
    trackingNumber: 'SF1122334455',
    weight: '0.9kg',
    paymentMethod: '寄付',
    amount: 0
  }
]

// 消息数据
export const messages = [
  {
    id: 1,
    type: 'system',
    title: '新取件订单',
    content: '您有一个新的取件订单，请及时处理',
    time: '2024-09-17 09:30',
    read: false
  },
  {
    id: 2,
    type: 'system', 
    title: '订单超时提醒',
    content: '订单SF1234567890即将超时，请尽快派送',
    time: '2024-09-17 10:15',
    read: false
  },
  {
    id: 3,
    type: 'customer',
    title: '客户催促',
    content: '客户张女士催促派送订单SF0987654321',
    time: '2024-09-17 11:00',
    read: true
  }
]

// 统计数据
export const statistics = {
  today: {
    pickup: 8,
    delivery: 12,
    overdue: 3,
    picked: 15,
    signed: 22
  },
  thisWeek: {
    pickup: 45,
    delivery: 67,
    picked: 89,
    signed: 123
  },
  thisMonth: {
    pickup: 180,
    delivery: 245,
    picked: 356,
    signed: 489
  }
}

// 用户信息
export const userInfo = {
  id: 'U001',
  name: '张三',
  phone: '138****8888',
  avatar: '/static/avatar.png',
  workNumber: 'YG001',
  department: '北京配送中心',
  rating: 4.8,
  level: '金牌快递员',
  workArea: '海淀区中关村片区'
}

// 运费计算规则
export const freightRules = {
  baseWeight: 1, // 首重1kg
  basePrice: 12, // 首重价格12元
  additionalWeight: 0.5, // 续重0.5kg
  additionalPrice: 3, // 续重价格3元
  longDistanceThreshold: 500, // 长途阈值500km
  longDistanceMultiplier: 1.5 // 长途倍数
}

// 导出所有数据的函数
export function getAllMockData() {
  return {
    pickupTasks,
    deliveryTasks,
    messages,
    statistics,
    userInfo,
    freightRules,
    TASK_STATUS,
    TASK_TYPE
  }
}

// 根据状态筛选任务
export function getTasksByStatus(tasks, status) {
  return tasks.filter(task => task.status === status)
}

// 根据类型获取任务
export function getTasksByType(type) {
  return type === TASK_TYPE.PICKUP ? pickupTasks : deliveryTasks
}

// 获取超时任务
export function getOverdueTasks() {
  const allTasks = [...pickupTasks, ...deliveryTasks]
  return allTasks.filter(task => task.status === TASK_STATUS.OVERDUE)
}
