<template>
  <view class="theme-switcher">
    <button
      v-for="(theme, index) in themes"
      :key="index"
      :class="['theme-btn', { active: currentTheme === theme }]"
      @click="switchTheme(theme)">
      {{ theme }}
    </button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      themes: ['light', 'dark', 'system'],
      currentTheme: 'system'
    };
  },
  methods: {
    switchTheme(theme) {
      this.currentTheme = theme;
      uni.setStorageSync('theme', theme);
      this.applyTheme();
    },
    applyTheme() {
      const theme = this.currentTheme;
      // 使用uni-app兼容的方式应用主题
      uni.setStorageSync('currentTheme', theme);

      // 发送全局事件通知主题变更
      uni.$emit('themeChanged', theme);

      // 在H5平台使用document操作
      // #ifdef H5
      if (theme === 'system') {
        document.body.classList.remove('light-theme', 'dark-theme');
      } else {
        document.body.classList.remove('light-theme', 'dark-theme');
        document.body.classList.add(`${theme}-theme`);
      }
      // #endif

      // 在其他平台通过设置data-theme属性
      // #ifndef H5
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage.$vm) {
          currentPage.$vm.$el.setAttribute('data-theme', theme);
        }
      }
      // #endif
    }
  },
  mounted() {
    const savedTheme = uni.getStorageSync('theme') || 'system';
    this.switchTheme(savedTheme);
  }
};
</script>

<style>
.theme-switcher {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.theme-btn {
  padding: 10rpx 20rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.theme-btn.active {
  background-color: #4caf50;
  color: #fff;
}
</style>
