<template>
  <view class="exclusive-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">专属取寄</text>
      <view class="header-right"></view>
    </view>

    <!-- 功能介绍 -->
    <view class="intro-section">
      <view class="intro-card">
        <text class="intro-title">专属取寄服务</text>
        <text class="intro-desc">为VIP客户提供专属的取件和寄件服务，享受优先处理和特殊包装</text>
      </view>
    </view>

    <!-- 服务类型 -->
    <view class="service-types">
      <view class="section-title">服务类型</view>
      <view class="service-list">
        <view class="service-item" @click="selectService('pickup')">
          <text class="icon">📦</text>
          <text class="label">专属取件</text>
          <text class="arrow">></text>
        </view>
        <view class="service-item" @click="selectService('delivery')">
          <text class="icon">🚚</text>
          <text class="label">专属派送</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 服务优势 -->
    <view class="advantages">
      <view class="section-title">服务优势</view>
      <view class="advantage-list">
        <view class="advantage-item">
          <text class="icon">⏱️</text>
          <text class="label">优先处理</text>
        </view>
        <view class="advantage-item">
          <text class="icon">🎁</text>
          <text class="label">特殊包装</text>
        </view>
        <view class="advantage-item">
          <text class="icon">👤</text>
          <text class="label">专人服务</text>
        </view>
        <view class="advantage-item">
          <text class="icon">📞</text>
          <text class="label">全程跟进</text>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="section-title">专属客服</view>
      <view class="contact-info">
        <text class="phone">📞 ************</text>
        <text class="desc">24小时专属客服为您服务</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedService: 'pickup' // 默认选择取件服务
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectService(service) {
      this.selectedService = service;
    }
  }
}
</script>

<style scoped>
/* 容器 */
.exclusive-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  width: 80rpx;
}

/* 功能介绍 */
.intro-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.intro-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.intro-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.intro-desc {
  font-size: 26rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

/* 服务类型 */
.service-types {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.service-item:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.label {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-dark);
}

.arrow {
  font-size: 32rpx;
  color: var(--text-gray);
}

/* 服务优势 */
.advantages {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.advantage-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.advantage-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
}

.advantage-item .icon {
  font-size: 36rpx;
  margin-right: 15rpx;
}

.advantage-item .label {
  font-size: 26rpx;
  color: var(--text-dark);
}

/* 联系方式 */
.contact-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.contact-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.phone {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.desc {
  font-size: 24rpx;
  color: var(--text-gray);
}
</style>
