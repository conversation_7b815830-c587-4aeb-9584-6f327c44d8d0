<template>
  <!-- 启动页容器 -->
  <view class="launch-container">
    <!-- Logo动画容器 -->
    <view class="logo-container">
      <!-- 应用Logo -->
      <image
        class="app-logo"
        :class="{ 'logo-animation': startAnimation }"
        src="/static/logo.png"
        mode="aspectFit"
      ></image>

      <!-- 应用名称 -->
      <text
        class="app-name"
        :class="{ 'name-animation': startAnimation }"
      >快递员端</text>

      <!-- 加载指示器 -->
      <view class="loading-dots">
        <text
          class="dot"
          :class="{ 'dot-animation': startAnimation }"
          :style="{ animationDelay: '0ms' }"
        >.</text>
        <text
          class="dot"
          :class="{ 'dot-animation': startAnimation }"
          :style="{ animationDelay: '200ms' }"
        >.</text>
        <text
          class="dot"
          :class="{ 'dot-animation': startAnimation }"
          :style="{ animationDelay: '400ms' }"
        >.</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LaunchPage',
  data() {
    return {
      // 控制动画开始的标志
      startAnimation: false
    }
  },

  mounted() {
    // 页面加载完成后开始动画
    this.startAnimation = true;

    // 3秒后跳转到登录页面
    setTimeout(() => {
      this.navigateToLogin();
    }, 3000);
  },

  methods: {
    /**
     * 跳转到登录页面
     */
    navigateToLogin() {
      // 使用滑动动画跳转到登录页面
      uni.redirectTo({
        url: '/pages/login/login',
        animationType: 'slide-in-right',
        animationDuration: 500
      });
    }
  }
}
</script>

<style scoped>
.launch-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #3498db, #8e44ad);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.app-logo {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 50rpx;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.8s ease-out;
}

.app-logo.logo-animation {
  opacity: 1;
  transform: scale(1);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 100rpx;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.8s ease-out 0.3s;
}

.app-name.name-animation {
  opacity: 1;
  transform: translateY(0);
}

.loading-dots {
  display: flex;
  flex-direction: row;
  z-index: 9999;
  position: relative;
}

.dot {
  font-size: 60rpx;
  color: #ffffff;
  opacity: 0;
  animation: none;
  margin: 0 5rpx;
}

.dot.dot-animation {
  animation: dotBlink 1.5s infinite;
}

@keyframes dotBlink {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
</style>
