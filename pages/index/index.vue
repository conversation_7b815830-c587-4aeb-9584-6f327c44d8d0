<template>
  <!-- 首页容器 -->
  <scroll-view
    class="home-container"
    scroll-y="true"
    refresher-enabled="true"
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
    @refresherrestore="onRefreshRestore"
    refresher-default-style="black"
    refresher-background="#f8f9fa"
    enhanced="true"
    show-scrollbar="false"
    :style="{ 'z-index': 1001 }">

    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 顶部搜索栏 -->
    <view class="search-bar" @click="goToSearch">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">输入运单号/手机号/姓名关键词</text>
      <view class="search-actions">
        <text class="message-icon" @click.stop="goToMessageCenter">✉️</text>
        <text v-if="unreadMessages > 0" class="unread-badge">{{ unreadMessages > 99 ? '99+' : unreadMessages }}</text>
      </view>
    </view>

    <!-- 核心任务概览 -->
    <view class="task-overview" :style="{ 'margin-top': 'calc(env(safe-area-inset-top) + 120rpx)' }">
      <view class="task-card" @click="goToTaskList('pickup')">
        <text class="card-title">取件任务</text>
        <text class="card-value">{{ taskStats.pickup }}</text>
      </view>
      <view class="task-card" @click="goToTaskList('delivery')">
        <text class="card-title">派件任务</text>
        <text class="card-value">{{ taskStats.delivery }}</text>
      </view>
      <view class="task-card" @click="goToTaskList('overdue')">
        <text class="card-title">超时任务</text>
        <text class="card-value">{{ taskStats.overdue }}</text>
      </view>
    </view>

    <!-- 滚动消息栏 -->
    <view class="message-scroll">
      <text class="message-icon">📢</text>
      <swiper class="message-swiper" vertical autoplay interval="3000" circular>
        <swiper-item v-for="(message, index) in messages" :key="index" @click="goToMessageDetail(message.id)">
          <text class="message-text">{{ message.content }}</text>
        </swiper-item>
      </swiper>
    </view>

    <!-- 常用功能区 -->
    <view class="quick-actions">
      <view class="action-item" @click="scanCode">
        <text class="action-icon">📷</text>
        <text class="action-label">签收扫描</text>
      </view>
      <view class="action-item" @click="goToAllTasks">
        <text class="action-icon">📋</text>
        <text class="action-label">全部取派</text>
      </view>
      <view class="action-item" @click="goToSignNotice">
        <text class="action-icon">🔔</text>
        <text class="action-label">签收提醒</text>
      </view>
      <view class="action-item" @click="goToFreightQuery">
        <text class="action-icon">💰</text>
        <text class="action-label">运费查询</text>
      </view>
      <view class="action-item" @click="goToExclusivePickup">
        <text class="action-icon">📦</text>
        <text class="action-label">专属取寄</text>
      </view>
    </view>

    <!-- 今日数据统计 -->
    <view class="today-stats">
      <view class="stat-item">
        <text class="stat-label">今日已取</text>
        <text class="stat-value">{{ todayStats.picked }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日已签</text>
        <text class="stat-value">{{ todayStats.signed }}</text>
      </view>
    </view>

    <!-- 待办任务列表 -->
    <view class="pending-tasks">
      <view class="section-header">
        <text class="section-title">待办任务</text>
        <text class="view-all" @click="goToAllTasks">查看更多</text>
      </view>
      <view class="task-list">
        <view class="task-card" v-for="task in pendingTasks" :key="task.id" @click="goToTaskDetail(task.id)">
          <!-- 任务类型标识 -->
          <view class="task-type-badge" :class="task.type">
            <text class="type-icon">{{ task.type === 'pickup' ? '📦' : '🚚' }}</text>
            <text class="type-text">{{ task.type === 'pickup' ? '取件' : '派件' }}</text>
          </view>

          <!-- 主要信息区域 -->
          <view class="task-main-info">
            <!-- 客户信息 -->
            <view class="customer-section">
              <view class="customer-header">
                <text class="customer-name">{{ task.customerName }}</text>
                <view class="distance-badge">
                  <text class="distance-text">{{ task.distance }}km</text>
                </view>
              </view>
              <text class="customer-phone">{{ task.customerPhone }}</text>
            </view>

            <!-- 地址信息 -->
            <view class="address-section">
              <text class="address-label">{{ task.type === 'pickup' ? '取件地址' : '派送地址' }}</text>
              <text class="address-text">{{ task.address }}</text>
            </view>

            <!-- 时间信息 -->
            <view class="time-section">
              <text class="time-label">预约时间</text>
              <text class="time-text">{{ task.time }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-section">
            <view class="action-buttons">
              <button class="action-btn call-btn" @click.stop="callCustomer(task.customerPhone)">
                <text class="btn-icon">📞</text>
                <text class="btn-text">电话</text>
              </button>
              <button class="action-btn message-btn" @click.stop="sendMessage(task.customerPhone)">
                <text class="btn-icon">💬</text>
                <text class="btn-text">短信</text>
              </button>
              <button v-if="task.type === 'pickup'" class="action-btn cancel-btn" @click.stop="cancelTask(task.id)">
                <text class="btn-icon">❌</text>
                <text class="btn-text">取消</text>
              </button>
              <button v-else class="action-btn deliver-btn" @click.stop="goToTaskDetail(task.id)">
                <text class="btn-icon">✅</text>
                <text class="btn-text">派送</text>
              </button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="pendingTasks.length === 0">
          <text class="empty-icon">📋</text>
          <text class="empty-text">暂无待办任务</text>
          <text class="empty-desc">您的任务列表是空的</text>
        </view>
      </view>
    </view>

  </scroll-view>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { statistics, messages, pickupTasks, deliveryTasks, getTasksByStatus, TASK_STATUS } from '@/utils/mockData.js'

export default {
  setup() {
    // 响应式数据
    const unreadMessages = ref(messages.filter(msg => !msg.read).length)
    const taskStats = reactive(statistics.today)
    const todayStats = reactive({
      picked: statistics.today.picked,
      signed: statistics.today.signed
    })
    const messageList = ref(messages)
    const pendingTasks = ref([])
    const refreshing = ref(false)

    // 加载待办任务
    const loadPendingTasks = () => {
      // 加载待办任务（取件和派件各取前3个）
      const pendingPickup = getTasksByStatus(pickupTasks, TASK_STATUS.PENDING).slice(0, 2)
      const pendingDelivery = getTasksByStatus(deliveryTasks, TASK_STATUS.PENDING).slice(0, 2)

      // 转换取件任务数据格式
      const pickupFormatted = pendingPickup.map(task => ({
        id: task.id,
        type: 'pickup',
        customerName: task.senderName,
        customerPhone: task.senderPhone,
        pickupAddress: task.pickupAddress,
        deliveryAddress: task.deliveryAddress,
        itemName: task.itemName || '待填写',
        weight: task.weight || '待称重',
        paymentType: task.paymentType || 'cash', // 'cash' 或 'cod'
        distance: task.distance,
        time: task.pickupTime
      }))

      // 转换派件任务数据格式
      const deliveryFormatted = pendingDelivery.map(task => ({
        id: task.id,
        type: 'delivery',
        recipientName: task.recipientName || task.customerName,
        recipientPhone: task.recipientPhone || task.customerPhone,
        deliveryAddress: task.deliveryAddress,
        trackingNumber: task.trackingNumber || 'TC' + task.id,
        itemName: task.itemName || '包裹',
        weight: task.weight || '1.0kg',
        codAmount: task.codAmount, // 代收金额
        distance: task.distance,
        time: task.deliveryTime
      }))

      pendingTasks.value = [...pickupFormatted, ...deliveryFormatted]
    }

    // 下拉刷新方法
    const onRefresh = () => {
      refreshing.value = true
      // 模拟刷新数据
      setTimeout(() => {
        loadPendingTasks()
        // 更新统计数据
        taskStats.pickup = pickupTasks.filter(task => task.status === 'pending').length
        taskStats.delivery = deliveryTasks.filter(task => task.status === 'pending').length
        taskStats.overdue = [...pickupTasks, ...deliveryTasks].filter(task => task.isOverdue).length

        refreshing.value = false
      }, 1500)
    }

    const onRefreshRestore = () => {
      refreshing.value = false
    }

    // 页面跳转方法
    const goToSearch = () => {
      uni.navigateTo({
        url: '/pages/search/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToMessageCenter = () => {
      uni.navigateTo({
        url: '/pages/message/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToTaskList = (filter) => {
      if (filter === 'pickup') {
        uni.switchTab({
          url: '/pages/task/list'
        })
      } else if (filter === 'delivery') {
        uni.switchTab({
          url: '/pages/task/delivery-list'
        })
      } else if (filter === 'overdue') {
        uni.navigateTo({
          url: '/pages/task/list?overdue=true',
          animationType: 'slide-in-right',
          animationDuration: 300
        })
      }
    }

    const goToMessageDetail = (messageId) => {
      console.log('跳转到消息详情，ID:', messageId)
    }

    const scanCode = () => {
      uni.scanCode({
        success: (res) => {
          uni.showToast({
            title: '扫码成功: ' + res.result,
            icon: 'none'
          })
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    }

    const goToAllTasks = () => {
      uni.navigateTo({
        url: '/pages/task/all',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToSignNotice = () => {
      uni.navigateTo({
        url: '/pages/message/index?tab=system&subTab=sign',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToFreightQuery = () => {
      uni.navigateTo({
        url: '/pages/freight/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToExclusivePickup = () => {
      uni.navigateTo({
        url: '/pages/exclusive/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToTaskDetail = (taskId) => {
      uni.navigateTo({
        url: '/pages/task/detail?id=' + taskId,
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const callCustomer = (phone) => {
      uni.makePhoneCall({
        phoneNumber: phone
      })
    }

    const sendMessage = (phone) => {
      uni.showToast({
        title: '发送短信功能开发中',
        icon: 'none'
      })
    }

    const goToPickup = (taskId) => {
      uni.navigateTo({
        url: '/pages/task/pickup?id=' + taskId,
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToDelivery = (taskId) => {
      uni.navigateTo({
        url: '/pages/task/delivery?id=' + taskId,
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const cancelTask = (taskId) => {
      uni.showModal({
        title: '取消任务',
        content: '确定要取消该任务吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '任务已取消',
              icon: 'success'
            })
          }
        }
      })
    }

    const goToPickup = () => {
      uni.switchTab({
        url: '/pages/task/list'
      })
    }

    const goToDelivery = () => {
      uni.switchTab({
        url: '/pages/task/delivery-list'
      })
    }

    const goToProfile = () => {
      uni.switchTab({
        url: '/pages/profile/index'
      })
    }

    // 生命周期
    onMounted(() => {
      loadPendingTasks()
    })

    // 返回响应式数据和方法
    return {
      unreadMessages,
      taskStats,
      todayStats,
      messages: messageList,
      pendingTasks,
      refreshing,
      loadPendingTasks,
      onRefresh,
      onRefreshRestore,
      goToSearch,
      goToMessageCenter,
      goToTaskList,
      goToMessageDetail,
      scanCode,
      goToAllTasks,
      goToSignNotice,
      goToFreightQuery,
      goToExclusivePickup,
      goToTaskDetail,
      callCustomer,
      sendMessage,
      cancelTask,
      goToPickup,
      goToDelivery,
      goToProfile
    }
  }
}
</script>

<style scoped>
/* 首页容器 */
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding: 0 0 calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

/* 顶部搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: fixed;  /* 固定定位 */
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: rgba(255, 255, 255, 0.8);
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  opacity: 0.8;
  color: rgba(255, 255, 255, 0.8);
}

.search-actions {
  position: relative;
  display: flex;
  align-items: center;
}

.message-icon {
  font-size: 36rpx;
  padding: 10rpx;
}

.unread-badge {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background-color: #e74c3c;
  color: white;
  font-size: 20rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 核心任务概览 */
.task-overview {
  display: flex;
  padding: 30rpx;
  background-color: var(--bg-white);
  margin: 140rpx 20rpx 20rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.task-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  min-height: 120rpx;
}

.task-card:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

.card-title {
  font-size: 26rpx;
  color: var(--text-gray);
  margin-bottom: 12rpx;
  text-align: center;
  line-height: 1.2;
}

.card-value {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1;
}

/* 消息滚动栏 */
.message-scroll {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  background-color: var(--bg-white);
  margin-bottom: 20rpx;
}

.message-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: var(--primary-color);
}

.message-swiper {
  flex: 1;
  height: 40rpx;
}

.message-text {
  font-size: 26rpx;
  color: var(--text-gray);
}

/* 常用功能区 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background-color: var(--bg-white);
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.action-label {
  font-size: 24rpx;
  color: var(--text-dark);
}

/* 今日数据统计 */
.today-stats {
  display: flex;
  padding: 30rpx;
  background-color: var(--bg-white);
  margin: 20rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 待办任务列表 */
.pending-tasks {
  flex: 1;
  background-color: var(--bg-white);
  margin: 20rpx 0;
  padding: 0 30rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
}

.task-list {
  padding: 20rpx 0;
}

/* 任务卡片样式 - 照片风格 */
.task-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #ffffff;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.task-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15), 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-card:last-child {
  margin-bottom: 0;
}

/* 取件卡片特殊样式 */
.pickup-card {
  border-left: 6rpx solid #3498db;
}

.pickup-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #3498db, #5dade2);
}

/* 派件卡片特殊样式 */
.delivery-card {
  border-left: 6rpx solid #e74c3c;
}

.delivery-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #e74c3c, #ec7063);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
}

/* 任务类型标识 */
.task-type-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.task-type-badge.pickup {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.task-type-badge.delivery {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.type-icon {
  margin-right: 6rpx;
  font-size: 20rpx;
}

.type-text {
  font-size: 22rpx;
  font-weight: 600;
}

/* 距离标识 */
.distance-badge {
  display: flex;
  align-items: center;
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.distance-icon {
  margin-right: 4rpx;
  font-size: 18rpx;
}

.distance-text {
  font-size: 22rpx;
}

/* 客户信息 */
.customer-info {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: rgba(248, 249, 250, 0.8);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
}

.avatar-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.customer-details {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
  display: block;
}

.customer-phone {
  font-size: 28rpx;
  color: var(--text-gray);
  display: block;
}

/* 取件信息 */
.pickup-info, .delivery-info {
  padding: 20rpx 24rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: var(--text-gray);
  width: 140rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.payment-type.cash {
  color: #e74c3c;
  font-weight: bold;
}

.payment-type:not(.cash) {
  color: #3498db;
  font-weight: bold;
}

.tracking-number {
  font-family: 'Courier New', monospace;
  background: rgba(52, 152, 219, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  color: #3498db;
  font-weight: bold;
}

.cod-amount {
  color: #e74c3c;
  font-weight: bold;
  font-size: 30rpx;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  padding: 20rpx 24rpx;
  background: rgba(248, 249, 250, 0.5);
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 6rpx;
  display: block;
}

.btn-text {
  font-size: 22rpx;
  display: block;
}

/* 电话按钮 */
.call-btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.call-btn:active {
  background: linear-gradient(135deg, #229954, #27ae60);
}

/* 短信按钮 */
.message-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.message-btn:active {
  background: linear-gradient(135deg, #e67e22, #d35400);
}

/* 取件按钮 */
.pickup-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.pickup-btn:active {
  background: linear-gradient(135deg, #2980b9, #21618c);
}

/* 派送按钮 */
.delivery-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.delivery-btn:active {
  background: linear-gradient(135deg, #c0392b, #a93226);
}

.time-text {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
  border-top: 1rpx solid var(--border-color);
  padding-top: 16rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 24rpx;
  min-height: 60rpx;
  min-width: 120rpx;
}

.call-btn {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.message-btn {
  background-color: #fff3e0;
  color: #f57c00;
}

.cancel-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.deliver-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.btn-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 底部导航栏 */
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-color);
  display: flex;
  padding: 10rpx 0 env(safe-area-inset-bottom);
  z-index: 999;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
}

.scan-center {
  flex: 1.5;
  background-color: var(--primary-color);
  border-radius: 50rpx;
  margin: 0 20rpx;
  color: var(--text-white);
}
</style>
