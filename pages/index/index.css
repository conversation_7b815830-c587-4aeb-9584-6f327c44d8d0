/* 首页样式 */

/* 首页容器 */
.home-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, #5dade2 100%);
  padding-top: env(safe-area-inset-top);
}

/* 状态栏占位 */
.status-bar {
  height: env(safe-area-inset-top);
  background: transparent;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: transparent;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius-large);
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  backdrop-filter: blur(10px);
}

.search-icon {
  font-size: 32rpx;
  color: var(--text-gray);
  margin-right: 20rpx;
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-gray);
}

.message-center {
  position: relative;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.message-icon {
  font-size: 40rpx;
  color: white;
}

.message-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: var(--danger-color);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 核心任务概览 */
.task-overview {
  display: flex;
  justify-content: space-between;
  margin: 0 40rpx 40rpx;
  gap: 20rpx;
}

.task-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-large);
  padding: 40rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

.task-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

.card-title {
  display: block;
  font-size: 24rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.card-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 滚动消息栏 */
.message-scroll {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  margin: 0 40rpx 40rpx;
  border-radius: var(--border-radius);
  padding: 20rpx;
  backdrop-filter: blur(10px);
}

.message-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.message-swiper {
  flex: 1;
  height: 60rpx;
}

.message-text {
  font-size: 28rpx;
  color: var(--text-dark);
  line-height: 60rpx;
}

/* 常用功能区 */
.quick-functions {
  background: var(--bg-white);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
  padding: 40rpx;
  flex: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
  margin-bottom: 50rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: var(--bg-light);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
  background: var(--bg-gray);
}

.function-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.function-text {
  font-size: 24rpx;
  color: var(--text-dark);
  text-align: center;
}

/* 今日数据统计 */
.today-stats {
  display: flex;
  justify-content: space-around;
  background: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--success-color);
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-gray);
}

/* 待办任务列表 */
.pending-tasks {
  margin-bottom: 40rpx;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 新的任务卡片样式 */
.task-card {
  background: var(--bg-white);
  border-radius: var(--border-radius-large);
  padding: 30rpx;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border-left: 6rpx solid var(--primary-color);
}

.task-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-medium);
}

/* 任务类型标识 */
.task-type-badge {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: var(--border-radius);
  width: fit-content;
}

.task-type-badge.pickup {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.task-type-badge.delivery {
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.3);
}

.type-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.type-text {
  font-size: 24rpx;
  font-weight: bold;
  color: var(--text-dark);
}

/* 客户信息区域 */
.customer-section {
  margin-bottom: 20rpx;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.customer-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.distance-badge {
  background: var(--accent-color);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.distance-text {
  font-size: 20rpx;
  color: white;
}

.customer-phone {
  font-size: 26rpx;
  color: var(--text-gray);
}

/* 地址信息区域 */
.address-section {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: var(--bg-light);
  border-radius: var(--border-radius);
}

.address-label {
  display: block;
  font-size: 24rpx;
  color: var(--text-gray);
  margin-bottom: 8rpx;
}

.address-text {
  font-size: 28rpx;
  color: var(--text-dark);
  line-height: 1.4;
}

/* 时间信息区域 */
.time-section {
  margin-bottom: 25rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.time-label {
  font-size: 24rpx;
  color: var(--text-gray);
}

.time-text {
  font-size: 26rpx;
  color: var(--warning-color);
  font-weight: bold;
}

/* 操作按钮区域 */
.action-section {
  border-top: 1px solid var(--border-color);
  padding-top: 20rpx;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 20rpx;
  border-radius: var(--border-radius);
  border: none;
  font-size: 24rpx;
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.btn-text {
  font-size: 20rpx;
  color: inherit;
}

.call-btn {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.message-btn {
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
}

.cancel-btn {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
}

.deliver-btn {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-gray);
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-light);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .function-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .task-overview {
    flex-direction: column;
    gap: 15rpx;
  }

  .task-card {
    padding: 20rpx;
  }

  .action-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media screen and (max-width: 600rpx) {
  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .header {
    padding: 20rpx 30rpx;
  }

  .quick-functions {
    padding: 30rpx;
  }
}
