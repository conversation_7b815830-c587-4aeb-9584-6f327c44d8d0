<template>
  <view class="payment-success-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">支付成功</text>
      <view class="header-right"></view>
    </view>

    <!-- 成功状态 -->
    <view class="success-section">
      <view class="success-icon-container">
        <text class="success-icon">✅</text>
        <view class="success-animation"></view>
      </view>
      <text class="success-title">支付成功</text>
      <text class="success-desc">您已成功支付到付费用</text>
      <text class="success-amount">¥{{ paymentAmount }}</text>
    </view>

    <!-- 支付信息 -->
    <view class="payment-info">
      <view class="info-item">
        <text class="label">订单号:</text>
        <view class="value">
          <text>{{ orderId }}</text>
          <text class="copy-btn" @click="copyOrderId">复制</text>
        </view>
      </view>
      <view class="info-item">
        <text class="label">支付方式:</text>
        <text class="value">{{ paymentMethod }}</text>
      </view>
      <view class="info-item">
        <text class="label">支付时间:</text>
        <text class="value">{{ paymentTime }}</text>
      </view>
    </view>
```

C:\Users\<USER>\Documents\HBuilderProjects\sl-express-courier\pages\payment\success.vue
```vue
<<<<<<< SEARCH
    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn primary-btn" @click="goToTaskList">返回任务列表</button>
      <button class="action-btn secondary-btn" @click="goToHome">返回首页</button>
    </view>
    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn primary-btn" @click="goToTaskList">返回任务列表</button>
      <button class="action-btn secondary-btn" @click="goToHome">返回首页</button>
    </view>
```

C:\Users\<USER>\Documents\HBuilderProjects\sl-express-courier\pages\payment\success.vue
```javascript
<<<<<<< SEARCH
    // 页面跳转方法
    const goBack = () => {
      uni.navigateBack()
    }

    const goToTaskList = () => {
      uni.switchTab({
        url: '/pages/task/list',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToHome = () => {
      uni.switchTab({
        url: '/pages/index/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }
    // 页面跳转方法
    const goBack = () => {
      uni.navigateBack()
    }

    const goToTaskList = () => {
      uni.switchTab({
        url: '/pages/task/list',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToHome = () => {
      uni.switchTab({
        url: '/pages/index/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    // 复制订单号
    const copyOrderId = () => {
      uni.setClipboardData({
        data: orderId.value,
        success: () => {
          uni.showToast({
            title: '已复制订单号',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'error'
          })
        }
      })
    }
```

C:\Users\<USER>\Documents\HBuilderProjects\sl-express-courier\pages\payment\success.vue
```javascript
<<<<<<< SEARCH
    return {
      paymentAmount,
      paymentMethod,
      transactionId,
      paymentTime,
      orderId,
      goBack,
      goToTaskList,
      goToHome
    }
    return {
      paymentAmount,
      paymentMethod,
      transactionId,
      paymentTime,
      orderId,
      goBack,
      goToTaskList,
      goToHome,
      copyOrderId
    }

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn primary-btn" @click="goToTaskList">返回任务列表</button>
      <button class="action-btn secondary-btn" @click="goToHome">返回首页</button>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <text class="tips-title">温馨提示</text>
      <text class="tips-text">• 支付凭证已保存，可在历史记录中查看</text>
      <text class="tips-text">• 如有疑问，请联系客服：400-123-4567</text>
      <text class="tips-text">• 感谢您使用神领物流服务</text>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  setup() {
    const paymentAmount = ref('0.00')
    const paymentMethod = ref('微信支付')
    const transactionId = ref('')
    const paymentTime = ref('')
    const orderId = ref('')

    // 生成交易单号
    const generateTransactionId = () => {
      const timestamp = Date.now()
      const random = Math.floor(Math.random() * 10000)
      return `TXN${timestamp}${random}`
    }

    // 格式化时间
    const formatTime = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    // 页面跳转方法
    const goBack = () => {
      uni.navigateBack()
    }

    const goToTaskList = () => {
      uni.switchTab({
        url: '/pages/task/list',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const goToHome = () => {
      uni.switchTab({
        url: '/pages/index/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    // 页面加载时初始化数据
    onMounted(() => {
      // 从页面参数获取支付信息
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options || {}

      paymentAmount.value = options.amount || '15.50'
      paymentMethod.value = options.method || '微信支付'
      orderId.value = options.orderId || 'SF1234567890'
      transactionId.value = generateTransactionId()
      paymentTime.value = formatTime(new Date())
    })

    return {
      paymentAmount,
      paymentMethod,
      transactionId,
      paymentTime,
      orderId,
      goBack,
      goToTaskList,
      goToHome
    }
  }
}
</script>

<style scoped>
/* CSS变量 */
:root {
  --primary-color: #3498db;
  --success-color: #27ae60;
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --text-dark: #2c3e50;
  --text-gray: #7f8c8d;
  --border-color: #dcdde1;
  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
  --border-radius: 12rpx;
}

/* 页面容器 */
.payment-success-container {
  min-height: 100vh;
  background: var(--bg-light);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx 20rpx 40rpx;  /* 固定的顶部间距 */
  background: var(--primary-color);
  color: white;
  position: relative;
}

.header-left {
  position: absolute;
  left: 40rpx;
  display: flex;
  align-items: center;
  padding: 15rpx;
  cursor: pointer;
  min-width: 80rpx;
  min-height: 60rpx;
  justify-content: center;
}

.header-left:active {
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

.back-icon {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-align: center;
}

.header-right {
  position: absolute;
  right: 40rpx;
  width: 80rpx;
}

/* 成功状态 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: var(--bg-white);
  margin-bottom: 20rpx;
}

.success-icon-container {
  position: relative;
  margin-bottom: 40rpx;
}

.success-icon {
  font-size: 120rpx;
  color: var(--success-color);
}

.success-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  border: 4rpx solid var(--success-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
}

.success-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--success-color);
  margin-bottom: 20rpx;
}

.success-desc {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 支付信息 */
.payment-info {
  background: var(--bg-white);
  margin-bottom: 20rpx;
  padding: 40rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: var(--text-gray);
}

.value {
  font-size: 28rpx;
  color: var(--text-dark);
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.copy-btn {
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 8rpx;
  background: rgba(52, 152, 219, 0.1);
}

.success-amount {
  font-size: 42rpx;
  color: var(--success-color);
  font-weight: bold;
  margin-top: 20rpx;
}

/* 操作按钮 */
.action-section {
  padding: 40rpx;
  background: var(--bg-white);
  margin-bottom: 20rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: var(--border-radius);
  border: none;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.primary-btn {
  background: var(--primary-color);
  color: white;
}

.secondary-btn {
  background: var(--bg-light);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}

.action-btn:active {
  transform: scale(0.98);
}

/* 温馨提示 */
.tips-section {
  background: var(--bg-white);
  padding: 40rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 20rpx;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: var(--text-gray);
  line-height: 1.6;
  margin-bottom: 10rpx;
}
</style>
