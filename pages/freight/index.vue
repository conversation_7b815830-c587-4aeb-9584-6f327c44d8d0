<template>
  <view class="freight-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">运费查询</text>
      <view class="header-right"></view>
    </view>

    <!-- 查询表单 -->
    <view class="query-form">
      <view class="form-group">
        <text class="form-label">寄件地</text>
        <view class="location-selector" @click="selectPickupLocation">
          <text class="location-text">{{ pickupLocation || '请选择寄件地' }}</text>
          <text class="arrow">▼</text>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">收件地</text>
        <view class="location-selector" @click="selectDeliveryLocation">
          <text class="location-text">{{ deliveryLocation || '请选择收件地' }}</text>
          <text class="arrow">▼</text>
        </view>
      </view>

      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">重量(kg)</text>
          <input
            class="form-input"
            type="digit"
            v-model="packageWeight"
            placeholder="请输入物品重量">
        </view>
        <view class="form-group half">
          <text class="form-label">体积(cm³)</text>
          <input
            class="form-input"
            type="digit"
            v-model="packageVolume"
            placeholder="请输入物品体积">
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">物品类型</text>
        <view class="type-selector">
          <view
            class="type-item"
            v-for="type in packageTypes"
            :key="type.value"
            :class="{ active: selectedPackageType === type.value }"
            @click="selectPackageType(type.value)"
          >
            {{ type.label }}
          </view>
        </view>
      </view>

      <button class="query-btn" @click="calculateFreight">查询运费</button>
    </view>

    <!-- 查询结果 -->
    <view class="result-section" v-if="freightResult">
      <view class="section-title">费用明细</view>
      <view class="fee-item">
        <text class="fee-label">首重费用</text>
        <text class="fee-value">¥{{ freightResult.baseFee }}</text>
      </view>
      <view class="fee-item">
        <text class="fee-label">续重费用</text>
        <text class="fee-value">¥{{ freightResult.additionalFee }}</text>
      </view>
      <view class="fee-item" v-if="freightResult.volumeFee > 0">
        <text class="fee-label">体积费用</text>
        <text class="fee-value">¥{{ freightResult.volumeFee }}</text>
      </view>
      <view class="fee-item" v-if="freightResult.specialItemFee > 0">
        <text class="fee-label">特殊物品费</text>
        <text class="fee-value">¥{{ freightResult.specialItemFee }}</text>
      </view>
      <view class="fee-divider"></view>
      <view class="fee-item total">
        <text class="fee-label">总计</text>
        <text class="fee-value total-value">¥{{ freightResult.totalFee }}</text>
      </view>
    </view>

    <!-- 运费说明 -->
    <view class="freight-info">
      <view class="info-title">运费说明</view>
      <view class="info-content">
        <text class="info-text">1. 首重1kg内费用为¥8，续重每1kg费用为¥2</text>
        <text class="info-text">2. 体积重量大于实际重量时，按体积重量计费</text>
        <text class="info-text">3. 特殊物品(易碎品、液体等)需额外收费</text>
        <text class="info-text">4. 距离超过1000km需加收远程费</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pickupLocation: '',
      deliveryLocation: '',
      packageWeight: '',
      packageVolume: '',
      selectedPackageType: 'standard',
      packageTypes: [
        { label: '标准件', value: 'standard' },
        { label: '易碎品', value: 'fragile' },
        { label: '液体', value: 'liquid' },
        { label: '电子产品', value: 'electronics' }
      ],
      freightResult: null
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectPickupLocation() {
      // 选择寄件地
      uni.showToast({
        title: '选择寄件地',
        icon: 'none'
      });
    },
    selectDeliveryLocation() {
      // 选择收件地
      uni.showToast({
        title: '选择收件地',
        icon: 'none'
      });
    },
    selectPackageType(type) {
      // 选择物品类型
      this.selectedPackageType = type;
    },
    calculateFreight() {
      // 计算运费
      if (!this.pickupLocation || !this.deliveryLocation) {
        uni.showToast({
          title: '请选择寄件地和收件地',
          icon: 'none'
        });
        return;
      }

      if (!this.packageWeight && !this.packageVolume) {
        uni.showToast({
          title: '请输入重量或体积',
          icon: 'none'
        });
        return;
      }

      // 模拟计算结果
      this.freightResult = {
        baseFee: 8,
        additionalFee: 12,
        volumeFee: this.packageVolume > 0 ? 5 : 0,
        specialItemFee: this.selectedPackageType !== 'standard' ? 3 : 0,
        totalFee: 0
      };

      this.freightResult.totalFee =
        this.freightResult.baseFee +
        this.freightResult.additionalFee +
        this.freightResult.volumeFee +
        this.freightResult.specialItemFee;
    }
  }
}
</script>

<style scoped>
.freight-container {
  min-height: 100vh;
  background-color: var(--bg-light);
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}

.header-left {
  position: absolute;
  left: 30rpx;
  padding: 15rpx;
  cursor: pointer;
  min-width: 80rpx;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-left:active {
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

.back-icon {
  font-size: 36rpx;
  color: var(--text-white);
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: var(--text-white);
}

.header-right {
  position: absolute;
  right: 30rpx;
  width: 80rpx;
}

.query-form {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-dark);
  margin-bottom: 15rpx;
  font-weight: 500;
}

.location-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70rpx;
  padding: 0 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  border: 1rpx solid var(--border-color);
}

.location-text {
  font-size: 26rpx;
  color: var(--text-dark);
}

.arrow {
  font-size: 20rpx;
  color: var(--text-gray);
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.half {
  width: 50%;
}

.form-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 20rpx rgba(52, 152, 219, 0.2);
  outline: none;
}

.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.type-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
}

.type-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.query-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: var(--text-white);
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.query-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.4);
}

/* 查询结果 */
.result-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.fee-label {
  font-size: 26rpx;
  color: var(--text-dark);
}

.fee-value {
  font-size: 26rpx;
  color: var(--text-dark);
  font-weight: 500;
}

.fee-divider {
  height: 1rpx;
  background-color: var(--border-color);
  margin: 20rpx 0;
}

.total {
  font-weight: bold;
  border-top: 1rpx solid var(--border-color);
  padding-top: 20rpx;
}

.total-value {
  font-size: 32rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 运费说明 */
.freight-info {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-text {
  font-size: 26rpx;
  color: var(--text-gray);
  line-height: 1.5;
}
</style>
