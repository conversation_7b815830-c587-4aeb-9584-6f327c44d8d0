/* CSS变量定义 */
:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --accent-color: #1abc9c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --bg-gray: #ecf0f1;
  --text-dark: #2c3e50;
  --text-gray: #7f8c8d;
  --text-light: #bdc3c7;
  --border-color: #dcdde1;
  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
  --border-radius: 12rpx;
  --border-radius-large: 20rpx;
}

/* 个人中心容器 */
.profile-container {
  min-height: 100vh;
  background: var(--bg-light);
  padding-top: env(safe-area-inset-top);
}

/* 状态栏占位 */
.status-bar {
  height: env(safe-area-inset-top);
  background: transparent;
}

/* 用户信息头部 */
.profile-header {
  position: relative;
  padding: 40rpx 40rpx 80rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #5dade2 100%);
}

.user-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-large);
  padding: 40rpx;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-medium);
}

/* 用户头像区域 */
.user-avatar-section {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: var(--shadow-light);
}

.avatar-badge {
  position: absolute;
  top: -5rpx;
  right: 50%;
  transform: translateX(50%);
  background: var(--warning-color);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.badge-text {
  font-size: 20rpx;
  font-weight: bold;
}

/* 用户信息 */
.user-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 10rpx;
}

.user-title {
  font-size: 26rpx;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.user-meta {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.user-id, .user-department {
  font-size: 24rpx;
  color: var(--text-gray);
}

.user-contact {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.contact-item {
  font-size: 24rpx;
  color: var(--text-gray);
}

/* 编辑按钮 */
.edit-profile-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-light);
}

.edit-icon {
  font-size: 28rpx;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx 40rpx 20rpx;
  background: var(--bg-white);
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: 24rpx;
  color: var(--text-gray);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 40rpx 30rpx;
  background: var(--bg-white);
}

.stat-card {
  background: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:active {
  transform: scale(0.98);
  background: var(--bg-gray);
}

.stat-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-gray);
  margin-bottom: 8rpx;
}

.stat-trend {
  font-size: 20rpx;
  color: var(--success-color);
  background: rgba(39, 174, 96, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

/* 快捷功能 */
.quick-actions {
  margin-bottom: 20rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 0 40rpx 30rpx;
  background: var(--bg-white);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 10rpx;
  border-radius: var(--border-radius);
  background: var(--bg-light);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: var(--bg-gray);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-dark);
  text-align: center;
}

/* 设置菜单 */
.settings-section {
  margin-bottom: 20rpx;
}

.menu-group {
  background: var(--bg-white);
  margin-bottom: 20rpx;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: var(--bg-light);
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.menu-icon.wallet {
  background: rgba(241, 196, 15, 0.1);
}

.menu-icon.orders {
  background: rgba(52, 152, 219, 0.1);
}

.menu-icon.certificates {
  background: rgba(155, 89, 182, 0.1);
}

.menu-icon.settings {
  background: rgba(149, 165, 166, 0.1);
}

.menu-icon.help {
  background: rgba(46, 204, 113, 0.1);
}

.menu-icon.about {
  background: rgba(230, 126, 34, 0.1);
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 28rpx;
  color: var(--text-dark);
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: var(--text-gray);
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.menu-value {
  font-size: 24rpx;
  color: var(--text-gray);
}

.menu-arrow {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 退出登录 */
.logout-section {
  padding: 40rpx;
}

.logout-btn {
  width: 100%;
  height: 80rpx;
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: #c0392b;
}

.logout-icon {
  font-size: 32rpx;
}

.logout-text {
  font-size: 28rpx;
  font-weight: bold;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-text {
    font-size: 22rpx;
  }
}
