<template>
  <view class="profile-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left"></view>
      <text class="header-title">个人中心</text>
      <view class="header-right"></view>
    </view>

    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <image class="avatar" src="/static/images/avatar.png" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">张三工号: YG001北京朝阳分部</text>
          <text class="user-rating">4.8评分</text>
        </view>
      </view>

      <!-- 工作统计 -->
      <view class="work-stats">
        <view class="stat-item">
          <text class="stat-label">任务数</text>
          <text class="stat-value">22工作天数</text>
        </view>
      </view>
    </view>

    <!-- 今日工作统计 -->
    <view class="today-stats">
      <text class="section-title">今日工作统计</text>
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-icon">📦</text>
          <text class="stat-text">已取件</text>
        </view>
        <view class="stat-card">
          <text class="stat-icon">🚚</text>
          <text class="stat-text">已派件</text>
        </view>
        <view class="stat-card">
          <text class="stat-icon">💰</text>
          <text class="stat-text">收入(元)</text>
        </view>
        <view class="stat-card">
          <text class="stat-icon">⭐</text>
          <text class="stat-text">平均评分</text>
        </view>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-functions">
      <text class="section-title">快捷功能</text>
      <view class="function-grid">
        <view class="function-item" @click="goToSchedule">
          <text class="function-icon">📅</text>
          <text class="function-text">我的排班</text>
        </view>
        <view class="function-item" @click="goToWorkArea">
          <text class="function-icon">🗺️</text>
          <text class="function-text">作业范围</text>
        </view>
        <view class="function-item" @click="goToTraining">
          <text class="function-icon">📚</text>
          <text class="function-text">培训学习</text>
        </view>
        <view class="function-item" @click="goToSettings">
          <text class="function-icon">⚙️</text>
          <text class="function-text">系统设置</text>
        </view>
      </view>
    </view>

    <!-- 菜单列表 -->
    <view class="menu-list">
      <view class="menu-item" @click="goToPersonalInfo">
        <text class="menu-icon">👤</text>
        <text class="menu-text">个人信息</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToWorkStats">
        <text class="menu-icon">📊</text>
        <text class="menu-text">工作统计</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToMessageCenter">
        <text class="menu-icon">📧</text>
        <text class="menu-text">消息中心</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToHelpCenter">
        <text class="menu-icon">❓</text>
        <text class="menu-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="logout">
        <text class="menu-icon">🚪</text>
        <text class="menu-text">退出登录</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '张三',
        employeeId: 'YG001',
        department: '北京朝阳分部',
        rating: '4.8',
        tasksCompleted: 22,
        workDays: 22
      }
    }
  },
  methods: {

    goToPersonalInfo() {
      uni.navigateTo({
        url: '/pages/profile/personal-info'
      })
    },
    goToWorkStats() {
      uni.navigateTo({
        url: '/pages/statistics/index'
      })
    },
    goToMessageCenter() {
      uni.navigateTo({
        url: '/pages/message/index'
      })
    },
    goToHelpCenter() {
      uni.navigateTo({
        url: '/pages/help/index'
      })
    },
    goToSchedule() {
      uni.navigateTo({
        url: '/pages/work/schedule'
      })
    },
    goToWorkArea() {
      uni.navigateTo({
        url: '/pages/work/area'
      })
    },
    goToTraining() {
      uni.navigateTo({
        url: '/pages/training/index'
      })
    },
    goToSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      })
    },
    logout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: var(--bg-light);
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}

.header-left, .header-right {
  width: 80rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

/* 用户信息区域 */
.user-section {
  background-color: var(--bg-white);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: var(--bg-light);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.user-rating {
  font-size: 28rpx;
  color: var(--text-gray);
  display: block;
}

.work-stats {
  border-top: 1rpx solid var(--border-color);
  padding-top: 20rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-gray);
}

.stat-value {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: bold;
}

/* 今日工作统计 */
.today-stats {
  margin: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.stat-card {
  background-color: var(--bg-white);
  border-radius: 12rpx;
  padding: 25rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  display: block;
}

.stat-text {
  font-size: 26rpx;
  color: var(--text-gray);
  display: block;
}

/* 快捷功能 */
.quick-functions {
  margin: 20rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.function-item {
  background-color: var(--bg-white);
  border-radius: 12rpx;
  padding: 25rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.function-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  display: block;
}

.function-text {
  font-size: 26rpx;
  color: var(--text-color);
  display: block;
}

/* 菜单列表 */
.menu-list {
  margin: 20rpx;
  background-color: var(--bg-white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--bg-light);
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-color);
}

.menu-arrow {
  font-size: 28rpx;
  color: var(--text-gray);
}
</style>
/* 个人中心页面样式 */

/* 个人中心容器 */
.profile-container {
  height: 100vh;
  background: var(--bg-light);
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 悬浮用户信息头部 */
.fixed-profile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 用户信息头部 */
.profile-header {
  position: relative;
  padding: 80rpx 40rpx 40rpx;  /* 固定的顶部间距 */
  overflow: hidden;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #5dade2 100%);
}

.user-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-large);
  padding: 25rpx;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-medium);
}

/* 用户头像区域 */
.user-avatar-section {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.avatar-container {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 6rpx 18rpx rgba(0,0,0,0.15);
}

.avatar-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 156rpx;
  height: 156rpx;
  border: 3rpx solid var(--primary-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-ring 2s infinite;
}

.status-indicator {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid white;
}

.status-indicator.online {
  background: #4CAF50;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.3;
  }
}

/* 用户信息 */
.user-info {
  text-align: center;
  margin-bottom: 20rpx;
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 12rpx;
}

.user-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.user-title {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.vip-badge {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.3);
}

.vip-text {
  font-size: 20rpx;
  font-weight: bold;
}

.user-meta {
  display: flex;
  justify-content: center;
  gap: 25rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 20rpx;
}

.meta-text {
  font-size: 24rpx;
  color: var(--text-gray);
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.action-btn {
  width: 50rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

.action-icon {
  font-size: 24rpx;
}

/* 页面内容区域 */
.profile-content {
  margin-top: 200rpx;  /* 为固定头部留出空间 */
  padding-top: 20rpx;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 20rpx;
  background: var(--bg-white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
}

.section-title {
  flex: 1;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: 24rpx;
  color: var(--text-gray);
}

.view-more {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: var(--bg-light);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.view-more:active {
  background: var(--bg-gray);
}

.more-text {
  font-size: 24rpx;
  color: var(--primary-color);
}

.more-arrow {
  font-size: 20rpx;
  color: var(--primary-color);
}

.stats-container {
  padding: 0 40rpx 30rpx;
}

.primary-stats {
  margin-bottom: 30rpx;
}

.primary-stat-card {
  background: linear-gradient(135deg, var(--primary-color), #5dade2);
  border-radius: var(--border-radius-large);
  padding: 30rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.primary-stat-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.stat-value.primary {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.stat-footer {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stat-trend.positive {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.stat-desc {
  font-size: 22rpx;
  opacity: 0.8;
}

.secondary-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.stat-card.compact {
  background: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  border: 1rpx solid transparent;
}

.stat-card.compact:active {
  transform: scale(0.98);
  background: var(--bg-gray);
  border-color: var(--primary-color);
}

.stat-card.compact .stat-icon {
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8rpx;
}

.stat-card.compact .stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 4rpx;
}

.stat-card.compact .stat-label {
  font-size: 20rpx;
  color: var(--text-gray);
}

.stat-card.compact .stat-trend {
  font-size: 18rpx;
  color: var(--success-color);
  font-weight: bold;
  background: rgba(76, 175, 80, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

/* 快捷功能 */
.quick-actions {
  margin-bottom: 20rpx;
  background: var(--bg-white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 40rpx 30rpx;
}

.action-item.modern {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: var(--border-radius-large);
  transition: all 0.3s ease;
  background: var(--bg-light);
  border: 1rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.action-item.modern:active {
  transform: scale(0.98);
  border-color: var(--primary-color);
  background: rgba(52, 152, 219, 0.05);
}

.action-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  position: relative;
}

.action-icon-container.schedule {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon-container.area {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon-container.training {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon-container.feedback {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-icon-container .action-icon {
  font-size: 36rpx;
  filter: brightness(0) invert(1);
}

.action-text {
  font-size: 26rpx;
  color: var(--text-dark);
  font-weight: 500;
  margin-bottom: 5rpx;
}

.action-desc {
  font-size: 22rpx;
  color: var(--text-gray);
  text-align: center;
}

/* 设置菜单 */
.settings-section {
  margin-bottom: 30rpx;
  background: var(--bg-white);
}

.menu-group {
  padding: 0 40rpx 20rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.menu-icon.wallet {
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
}

.menu-icon.orders {
  background: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
}

.menu-icon.certificates {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.menu-icon.settings {
  background: rgba(241, 196, 15, 0.1);
  color: var(--warning-color);
}

.menu-icon.help {
  background: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

.menu-icon.about {
  background: rgba(149, 165, 166, 0.1);
  color: var(--text-gray);
}

.menu-content {
  display: flex;
  flex-direction: column;
}

.menu-title {
  font-size: 30rpx;
  color: var(--text-dark);
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: var(--text-gray);
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-value {
  font-size: 28rpx;
  color: var(--text-dark);
  margin-right: 15rpx;
  font-weight: bold;
}

.menu-arrow {
  font-size: 32rpx;
  color: var(--text-light);
}

/* 退出登录 */
.logout-section {
  padding: 0 40rpx 40rpx;
}

.logout-btn {
  background: var(--bg-white);
  border: 1rpx solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background: var(--bg-light);
  transform: scale(0.98);
}

.logout-icon {
  font-size: 36rpx;
  margin-right: 15rpx;
}

.logout-text {
  font-size: 32rpx;
  color: var(--danger-color);
  font-weight: bold;
}
</style>
