<template>
  <view class="delivery-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">去派件</text>
      <view class="header-right"></view>
    </view>

    <!-- 任务基本信息卡片 -->
    <view class="task-card">
      <view class="card-header">
        <text class="card-title">🚚 派件任务</text>
        <view class="task-status pending">
          <text class="status-text">待派送</text>
        </view>
      </view>

      <view class="task-info">
        <view class="info-row">
          <text class="info-label">运单号</text>
          <view class="info-value-group">
            <text class="info-value tracking-number">{{ taskInfo.trackingNumber }}</text>
            <button class="copy-btn" @click="copyTaskId">复制</button>
          </view>
        </view>
        <view class="info-row">
          <text class="info-label">派送时间</text>
          <text class="info-value">{{ taskInfo.deliveryTime }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">物品信息</text>
          <text class="info-value">{{ taskInfo.itemName }} ({{ taskInfo.weight }})</text>
        </view>
        <view class="info-row" v-if="taskInfo.codAmount">
          <text class="info-label">代收金额</text>
          <text class="info-value cod-amount">¥{{ taskInfo.codAmount }}</text>
        </view>
      </view>
    </view>

    <!-- 收件人信息卡片 -->
    <view class="recipient-card">
      <view class="card-header">
        <text class="card-title">👤 收件人信息</text>
        <view class="contact-actions">
          <button class="contact-btn call-btn" @click="callRecipient">
            <text class="btn-icon">📞</text>
          </button>
          <button class="contact-btn message-btn" @click="sendMessage">
            <text class="btn-icon">💬</text>
          </button>
        </view>
      </view>

      <view class="recipient-info">
        <view class="info-row">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ taskInfo.recipientName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">电话</text>
          <text class="info-value">{{ taskInfo.recipientPhone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">收货地址</text>
          <text class="info-value address">{{ taskInfo.deliveryAddress }}</text>
        </view>
      </view>
    </view>

    <!-- 签收方式选择 -->
    <view class="signature-card">
      <view class="card-header">
        <text class="card-title">✍️ 签收方式</text>
      </view>

      <view class="signature-options">
        <view
          class="signature-option"
          :class="{ active: signatureInfo.signType === 'self' }"
          @click="selectSignType('self')">
          <view class="option-icon">👤</view>
          <view class="option-content">
            <text class="option-title">本人签收</text>
            <text class="option-desc">收件人本人签收</text>
          </view>
          <view class="option-check" v-if="signatureInfo.signType === 'self'">✓</view>
        </view>

        <view
          class="signature-option"
          :class="{ active: signatureInfo.signType === 'proxy' }"
          @click="selectSignType('proxy')">
          <view class="option-icon">👥</view>
          <view class="option-content">
            <text class="option-title">代收</text>
            <text class="option-desc">他人代为签收</text>
          </view>
          <view class="option-check" v-if="signatureInfo.signType === 'proxy'">✓</view>
        </view>

        <view
          class="signature-option"
          :class="{ active: signatureInfo.signType === 'cabinet' }"
          @click="selectSignType('cabinet')">
          <view class="option-icon">📦</view>
          <view class="option-content">
            <text class="option-title">快递柜</text>
            <text class="option-desc">投放至快递柜</text>
          </view>
          <view class="option-check" v-if="signatureInfo.signType === 'cabinet'">✓</view>
        </view>
      </view>

      <!-- 代收人信息 -->
      <view class="proxy-info" v-if="signatureInfo.signType === 'proxy'">
        <view class="form-group">
          <text class="form-label">代收人姓名 *</text>
          <input
            class="form-input"
            type="text"
            v-model="signatureInfo.proxyName"
            placeholder="请输入代收人姓名">
        </view>
        <view class="form-group">
          <text class="form-label">代收人电话</text>
          <input
            class="form-input"
            type="text"
            v-model="signatureInfo.proxyPhone"
            placeholder="请输入代收人电话">
        </view>
      </view>

      <!-- 快递柜信息 -->
      <view class="cabinet-info" v-if="signatureInfo.signType === 'cabinet'">
        <view class="form-group">
          <text class="form-label">快递柜编号 *</text>
          <input
            class="form-input"
            type="text"
            v-model="signatureInfo.cabinetNumber"
            placeholder="请输入快递柜编号">
        </view>
        <view class="form-group">
          <text class="form-label">取件码</text>
          <input
            class="form-input"
            type="text"
            v-model="signatureInfo.pickupCode"
            placeholder="请输入取件码">
        </view>
      </view>
    </view>

    <!-- 代收金额处理 -->
    <view class="cod-card" v-if="taskInfo.codAmount">
      <view class="card-header">
        <text class="card-title">💰 代收金额</text>
      </view>

      <view class="cod-content">
        <view class="cod-amount-display">
          <text class="amount-label">需代收</text>
          <text class="amount-value">¥{{ taskInfo.codAmount }}</text>
        </view>

        <view class="cod-status">
          <view
            class="status-option"
            :class="{ active: signatureInfo.codCollected }"
            @click="signatureInfo.codCollected = !signatureInfo.codCollected">
            <view class="status-icon">{{ signatureInfo.codCollected ? '✅' : '⭕' }}</view>
            <text class="status-text">{{ signatureInfo.codCollected ? '已收取' : '未收取' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        class="delivery-btn"
        :disabled="!canDeliver"
        @click="handleDelivery">
        {{ taskInfo.codAmount && signatureInfo.codCollected ? '收费并完成派送' : '确认派送' }}
      </button>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      taskInfo: {
        trackingNumber: 'TC987654321',
        deliveryTime: '2023-04-01 14:00-16:00',
        itemName: '电子产品',
        weight: '2.5kg',
        codAmount: '299.00', // 代收金额，如果没有则为null
        recipientName: '李四',
        recipientPhone: '139****5678',
        deliveryAddress: '上海市浦东新区陆家嘴金融中心B座2301室'
      },
      signatureInfo: {
        signType: 'self', // 'self', 'proxy', 'cabinet'
        proxyName: '',
        proxyPhone: '',
        cabinetNumber: '',
        pickupCode: '',
        codCollected: false // 是否已收取代收金额
      }
    }
  },
  computed: {
    canDeliver() {
      if (this.signatureInfo.signType === 'proxy') {
        return this.signatureInfo.proxyName.trim()
      }
      if (this.signatureInfo.signType === 'cabinet') {
        return this.signatureInfo.cabinetNumber.trim()
      }
      if (this.taskInfo.codAmount) {
        return this.signatureInfo.codCollected
      }
      return true
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    copyTaskId() {
      uni.setClipboardData({
        data: this.taskInfo.trackingNumber,
        success: () => {
          uni.showToast({
            title: '已复制',
            icon: 'none'
          })
        }
      })
    },
    callRecipient() {
      uni.makePhoneCall({
        phoneNumber: this.taskInfo.recipientPhone.replace(/\*/g, '0')
      })
    },
    sendMessage() {
      uni.showToast({
        title: '发送短信功能开发中',
        icon: 'none'
      })
    },
    selectSignType(type) {
      this.signatureInfo.signType = type
      // 清空其他类型的数据
      if (type !== 'proxy') {
        this.signatureInfo.proxyName = ''
        this.signatureInfo.proxyPhone = ''
      }
      if (type !== 'cabinet') {
        this.signatureInfo.cabinetNumber = ''
        this.signatureInfo.pickupCode = ''
      }
    },
    handleDelivery() {
      if (!this.canDeliver) {
        uni.showToast({
          title: '请完善签收信息',
          icon: 'none'
        })
        return
      }

      if (this.taskInfo.codAmount && this.signatureInfo.codCollected) {
        // 有代收金额且已收取，跳转到付款成功页面
        this.processPayment()
      } else {
        // 直接完成派送
        this.confirmDelivery()
      }
    },
    processPayment() {
      // 跳转到付款成功页面
      uni.navigateTo({
        url: `/pages/payment/success?amount=${this.taskInfo.codAmount}&type=delivery`
      })
    },
    confirmDelivery() {
      const signTypeText = {
        'self': '本人签收',
        'proxy': `代收人：${this.signatureInfo.proxyName}`,
        'cabinet': `快递柜：${this.signatureInfo.cabinetNumber}`
      }

      uni.showModal({
        title: '确认派送',
        content: `签收方式：${signTypeText[this.signatureInfo.signType]}\n确认完成派送？`,
        success: (res) => {
          if (res.confirm) {
            this.showDeliverySuccess()
          }
        }
      })
    },
    showDeliverySuccess() {
      uni.showToast({
        title: '派送成功',
        icon: 'success'
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style scoped>
/* 容器 */
.delivery-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}

.header-left {
  position: absolute;
  left: 30rpx;
  padding: 15rpx;
  cursor: pointer;
  min-width: 80rpx;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-left:active {
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

.back-icon {
  font-size: 36rpx;
  color: var(--text-white);
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-white);
  text-align: center;
}

.header-right {
  position: absolute;
  right: 30rpx;
  width: 80rpx;
}

/* 卡片通用样式 */
.task-card, .recipient-card, .signature-card, .cod-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #ffffff;
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: rgba(231, 76, 60, 0.05);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

/* 任务状态 */
.task-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.task-status.pending {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

/* 联系操作按钮 */
.contact-actions {
  display: flex;
  gap: 12rpx;
}

.contact-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.call-btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.message-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.contact-btn:active {
  transform: scale(0.9);
}

/* 信息行 */
.task-info, .recipient-info {
  padding: 20rpx 30rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: var(--text-gray);
  width: 140rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  font-size: 30rpx;
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.info-value.address {
  line-height: 1.6;
  word-break: break-all;
}

.info-value-group {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16rpx;
}

.tracking-number {
  font-family: 'Courier New', monospace;
  background: rgba(231, 76, 60, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  color: #e74c3c;
  font-weight: bold;
}

.cod-amount {
  color: #e74c3c;
  font-weight: bold;
  font-size: 32rpx;
}

.copy-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

/* 签收方式选择 */
.signature-options {
  padding: 20rpx 30rpx;
}

.signature-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.signature-option:last-child {
  margin-bottom: 0;
}

.signature-option.active {
  border-color: var(--primary-color);
  background: rgba(52, 152, 219, 0.05);
}

.option-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 6rpx;
}

.option-desc {
  font-size: 24rpx;
  color: var(--text-gray);
  display: block;
}

.option-check {
  font-size: 32rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 表单样式 */
.proxy-info, .cabinet-info {
  padding: 20rpx 30rpx;
  background: rgba(248, 249, 250, 0.8);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 24rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(52, 152, 219, 0.1);
}

/* 代收金额处理 */
.cod-content {
  padding: 20rpx 30rpx;
}

.cod-amount-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(231, 76, 60, 0.05);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.amount-label {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 600;
}

.amount-value {
  font-size: 36rpx;
  color: #e74c3c;
  font-weight: bold;
}

.cod-status {
  display: flex;
  justify-content: center;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-option.active {
  border-color: #27ae60;
  background: rgba(39, 174, 96, 0.05);
}

.status-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
}

/* 操作按钮 */
.action-section {
  padding: 30rpx;
  background: var(--bg-white);
}

.delivery-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.delivery-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #c0392b, #a93226);
}

.delivery-btn:disabled {
  background: #bdc3c7;
  transform: none;
}
</style>
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.more-icon {
  font-size: 36rpx;
}

/* 内容区域 */
.order-info,
.recipient-info,
.package-info,
.signatory-selection {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: var(--text-gray);
}

.value {
  font-size: 26rpx;
  color: var(--text-dark);
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

/* 签收人选择 */
.signatory-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.option-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
}

.option-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.signatory-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

/* 核心操作 */
.action-section {
  padding: 20rpx 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.exception-btn {
  background-color: #e74c3c;
  color: var(--text-white);
}
</style>
