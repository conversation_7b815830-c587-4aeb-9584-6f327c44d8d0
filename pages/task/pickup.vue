<template>
  <view class="pickup-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">去取件</text>
      <view class="header-right"></view>
    </view>

    <!-- 任务基本信息卡片 -->
    <view class="task-card">
      <view class="card-header">
        <text class="card-title">📦 取件任务</text>
        <view class="task-status pending">
          <text class="status-text">待取件</text>
        </view>
      </view>

      <view class="task-info">
        <view class="info-row">
          <text class="info-label">任务单号</text>
          <view class="info-value-group">
            <text class="info-value">{{ taskInfo.taskNumber }}</text>
            <button class="copy-btn" @click="copyTaskId">复制</button>
          </view>
        </view>
        <view class="info-row">
          <text class="info-label">预约时间</text>
          <text class="info-value">{{ taskInfo.appointmentTime }}</text>
        </view>
      </view>
    </view>

    <!-- 寄件人信息卡片 -->
    <view class="sender-card">
      <view class="card-header">
        <text class="card-title">👤 寄件人信息</text>
        <view class="contact-actions">
          <button class="contact-btn call-btn" @click="callSender">
            <text class="btn-icon">📞</text>
          </button>
          <button class="contact-btn message-btn" @click="sendMessage">
            <text class="btn-icon">💬</text>
          </button>
        </view>
      </view>

      <view class="sender-info">
        <view class="info-row">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ taskInfo.senderName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">电话</text>
          <text class="info-value">{{ taskInfo.senderPhone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">取件地址</text>
          <text class="info-value address">{{ taskInfo.pickupAddress }}</text>
        </view>
      </view>
    </view>

    <!-- 收件人信息卡片 -->
    <view class="recipient-card">
      <view class="card-header">
        <text class="card-title">📍 派送目标</text>
      </view>

      <view class="recipient-info">
        <view class="info-row">
          <text class="info-label">收件人</text>
          <text class="info-value">{{ taskInfo.recipientName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">电话</text>
          <text class="info-value">{{ taskInfo.recipientPhone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">派送地址</text>
          <text class="info-value address">{{ taskInfo.deliveryAddress }}</text>
        </view>
      </view>
    </view>

    <!-- 物品信息填写 -->
    <view class="package-form">
      <view class="card-header">
        <text class="card-title">📦 物品信息</text>
        <text class="required-note">* 必填项</text>
      </view>

      <view class="form-content">
        <view class="form-group">
          <text class="form-label">物品名称 *</text>
          <input
            class="form-input"
            type="text"
            v-model="packageInfo.itemName"
            placeholder="请输入物品名称">
        </view>

        <view class="form-group">
          <text class="form-label">物品重量 *</text>
          <view class="input-with-unit">
            <input
              class="form-input"
              type="digit"
              v-model="packageInfo.weight"
              placeholder="请输入重量">
            <text class="unit">kg</text>
          </view>
        </view>

        <view class="form-group">
          <text class="form-label">物品体积</text>
          <view class="input-with-unit">
            <input
              class="form-input"
              type="digit"
              v-model="packageInfo.volume"
              placeholder="请输入体积">
            <text class="unit">cm³</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 付款方式选择 -->
    <view class="payment-card">
      <view class="card-header">
        <text class="card-title">💰 付款方式</text>
      </view>

      <view class="payment-options">
        <view
          class="payment-option"
          :class="{ active: packageInfo.paymentType === 'cash' }"
          @click="selectPaymentType('cash')">
          <view class="option-icon">💵</view>
          <view class="option-content">
            <text class="option-title">现场付款</text>
            <text class="option-desc">取件时立即收费</text>
          </view>
          <view class="option-check" v-if="packageInfo.paymentType === 'cash'">✓</view>
        </view>

        <view
          class="payment-option"
          :class="{ active: packageInfo.paymentType === 'cod' }"
          @click="selectPaymentType('cod')">
          <view class="option-icon">🚚</view>
          <view class="option-content">
            <text class="option-title">到付</text>
            <text class="option-desc">派送时收取费用</text>
          </view>
          <view class="option-check" v-if="packageInfo.paymentType === 'cod'">✓</view>
        </view>
      </view>

      <!-- 费用预估 -->
      <view class="fee-estimate" v-if="packageInfo.weight">
        <view class="fee-row">
          <text class="fee-label">预估费用</text>
          <text class="fee-value">¥{{ estimatedFee }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        class="pickup-btn"
        :disabled="!canPickup"
        @click="handlePickup">
        {{ packageInfo.paymentType === 'cash' ? '收费并取件' : '确认取件' }}
      </button>
    </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      taskInfo: {
        taskNumber: 'TC123456789',
        appointmentTime: '2023-04-01 10:00-12:00',
        senderName: '张三',
        senderPhone: '138****1234',
        pickupAddress: '北京市朝阳区建国路88号SOHO现代城A座1201室',
        recipientName: '李四',
        recipientPhone: '139****5678',
        deliveryAddress: '上海市浦东新区陆家嘴金融中心B座2301室'
      },
      packageInfo: {
        itemName: '',
        weight: '',
        volume: '',
        paymentType: 'cash' // 'cash' 或 'cod'
      }
    }
  },
  computed: {
    estimatedFee() {
      if (!this.packageInfo.weight) return '0.00'
      const basePrice = 12
      const weightPrice = Math.max(0, (parseFloat(this.packageInfo.weight) - 1)) * 3
      return (basePrice + weightPrice).toFixed(2)
    },
    canPickup() {
      return this.packageInfo.itemName.trim() && this.packageInfo.weight
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    copyTaskId() {
      uni.setClipboardData({
        data: this.taskInfo.taskNumber,
        success: () => {
          uni.showToast({
            title: '已复制',
            icon: 'none'
          })
        }
      })
    },
    callSender() {
      uni.makePhoneCall({
        phoneNumber: this.taskInfo.senderPhone.replace(/\*/g, '0')
      })
    },
    sendMessage() {
      uni.showToast({
        title: '发送短信功能开发中',
        icon: 'none'
      })
    },
    selectPaymentType(type) {
      this.packageInfo.paymentType = type
    },
    handlePickup() {
      if (!this.canPickup) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }

      if (this.packageInfo.paymentType === 'cash') {
        // 现场付款流程
        this.showPaymentDialog()
      } else {
        // 到付流程
        this.confirmPickup()
      }
    },
    showPaymentDialog() {
      uni.showModal({
        title: '收费确认',
        content: `费用总计：¥${this.estimatedFee}\n请确认已收取费用`,
        success: (res) => {
          if (res.confirm) {
            this.processPayment()
          }
        }
      })
    },
    processPayment() {
      // 跳转到付款成功页面
      uni.navigateTo({
        url: `/pages/payment/success?amount=${this.estimatedFee}&type=pickup`
      })
    },
    confirmPickup() {
      uni.showModal({
        title: '确认取件',
        content: '请确认已完成取件操作且信息填写正确？',
        success: (res) => {
          if (res.confirm) {
            this.showPickupSuccess()
          }
        }
      })
    },
    showPickupSuccess() {
      uni.showToast({
        title: '取件成功',
        icon: 'success'
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style scoped>
/* 容器 */
.pickup-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 状态栏占位 - 不再需要 */
.status-bar {
  display: none;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}

.header-left {
  position: absolute;
  left: 30rpx;
  padding: 15rpx;
  cursor: pointer;
  min-width: 80rpx;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-left:active {
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

.back-icon {
  font-size: 36rpx;
  color: var(--text-white);
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-white);
  text-align: center;
}

.header-right {
  position: absolute;
  right: 30rpx;
  width: 80rpx;
}

/* 卡片通用样式 */
.task-card, .sender-card, .recipient-card, .package-form, .payment-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #ffffff;
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: rgba(52, 152, 219, 0.05);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.required-note {
  font-size: 24rpx;
  color: #e74c3c;
}

/* 任务状态 */
.task-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.task-status.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

/* 联系操作按钮 */
.contact-actions {
  display: flex;
  gap: 12rpx;
}

.contact-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.call-btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.message-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.contact-btn:active {
  transform: scale(0.9);
}

/* 信息行 */
.task-info, .sender-info, .recipient-info {
  padding: 20rpx 30rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: var(--text-gray);
  width: 140rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  font-size: 30rpx;
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.info-value.address {
  line-height: 1.6;
  word-break: break-all;
}

.info-value-group {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16rpx;
}

.copy-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

/* 表单样式 */
.form-content {
  padding: 20rpx 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(52, 152, 219, 0.1);
}

.input-with-unit {
  display: flex;
  align-items: center;
  position: relative;
}

.input-with-unit .form-input {
  padding-right: 80rpx;
}

.unit {
  position: absolute;
  right: 20rpx;
  font-size: 26rpx;
  color: var(--text-gray);
  font-weight: 500;
}

/* 付款方式选择 */
.payment-options {
  padding: 20rpx 30rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.payment-option:last-child {
  margin-bottom: 0;
}

.payment-option.active {
  border-color: var(--primary-color);
  background: rgba(52, 152, 219, 0.05);
}

.option-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 6rpx;
}

.option-desc {
  font-size: 24rpx;
  color: var(--text-gray);
  display: block;
}

.option-check {
  font-size: 32rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 费用预估 */
.fee-estimate {
  padding: 20rpx 30rpx;
  background: rgba(52, 152, 219, 0.05);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.fee-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 600;
}

.fee-value {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
}

/* 操作按钮 */
.action-section {
  padding: 30rpx;
  background: var(--bg-white);
}

.pickup-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.pickup-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #2980b9, #21618c);
}

.pickup-btn:disabled {
  background: #bdc3c7;
  transform: none;
}
</style>
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.more-icon {
  font-size: 36rpx;
}

/* 内容区域 */
.task-info,
.package-info,
.fee-info,
.identity-verification {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: var(--text-gray);
}

.value {
  font-size: 26rpx;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.copy-btn,
.call-btn {
  font-size: 24rpx;
  color: var(--primary-color);
  margin-left: 20rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid var(--primary-color);
  border-radius: 6rpx;
}

.fee-item.total {
  border-top: 1rpx solid var(--border-color);
  padding-top: 20rpx;
  margin-top: 20rpx;
  font-weight: bold;
}

.total-value {
  color: #e74c3c;
  font-size: 32rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 24rpx;
}

.form-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
  margin-top: 12rpx;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.half {
  flex: 1;
}

/* 付款方式 */
.payment-options {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.option {
  flex: 1;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
  text-align: center;
}

.option.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

/* 身份验证 */
.verification-method {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.verification-input {
  display: flex;
  gap: 20rpx;
}

.code-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

.send-code-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  font-size: 26rpx;
  border-radius: 10rpx;
  border: none;
}

/* 操作按钮 */
.action-buttons {
  padding: 20rpx 30rpx;
}

.pickup-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border-radius: 10rpx;
  border: none;
}

/* 核心操作 */
.action-section {
  padding: 20rpx 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.cancel-btn {
  background-color: var(--bg-gray);
  color: var(--text-dark);
}
</style>
