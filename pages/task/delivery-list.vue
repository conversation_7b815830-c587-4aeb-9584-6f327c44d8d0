<template>
  <scroll-view 
    class="task-container" 
    scroll-y="true" 
    refresher-enabled="true"
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
    @refresherrestore="onRefreshRestore"
    show-scrollbar="false">
    
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <text class="header-title">派件列表</text>
    </view>

    <!-- 状态切换标签 -->
    <view class="status-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'pending' }"
        @click="switchStatus('pending')">
        待派件
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'completed' }"
        @click="switchStatus('completed')">
        已签收
      </view>
    </view>

    <!-- 筛选与排序 -->
    <view class="filter-sort-container">
      <view class="filter-options">
        <picker @change="onSortChange" :value="sortIndex" :range="sortOptions" range-key="label">
          <view class="filter-btn">
            <text>排序: {{ sortOptions[sortIndex].label }}</text>
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      <view class="search-box" @click="goToSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索任务</text>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view class="task-item" v-for="task in filteredTasks" :key="task.id" @click="goToTaskDetail(task.id)">
        <view class="task-header">
          <text class="task-id">任务单号: {{ task.id }}</text>
          <text class="task-status" :class="task.statusClass">{{ task.statusText }}</text>
        </view>
        <view class="task-content">
          <view class="task-address">
            <text class="label">收货地址:</text>
            <text class="address">{{ task.deliveryAddress }}</text>
          </view>
          <view class="task-time">
            <text class="label">派送时间:</text>
            <text class="time">{{ task.deliveryTime }}</text>
          </view>
          <view class="task-customer">
            <text class="label">收货人:</text>
            <text class="customer">{{ task.customerName }} {{ task.customerPhone }}</text>
          </view>
        </view>
        <view class="task-footer">
          <text class="distance">{{ task.distance }}km</text>
          <button class="action-btn" :class="task.actionClass" @click.stop="handleTaskAction(task)">{{ task.actionText }}</button>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredTasks.length === 0">
        <text class="empty-text">暂无任务</text>
      </view>
    </view>

    <!-- 批量管理 -->
    <view class="batch-management" v-if="showBatchManagement">
      <view class="batch-actions">
        <view class="selectAll">
          <checkbox :value="allSelected" @change="toggleSelectAll"></checkbox>
          <text>全选</text>
        </view>
        <view class="action-buttons">
          <button class="batch-btn" @click="batchComplete">完成</button>
          <button class="batch-btn delete" @click="batchDelete">删除</button>
        </view>
      </view>
    </view>

  </scroll-view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { deliveryTasks } from '@/utils/mockData.js'

export default {
  setup() {
    // 响应式数据
    const currentStatus = ref('pending')
    const sortIndex = ref(0)
    const showBatchManagement = ref(false)
    const allSelected = ref(false)
    const refreshing = ref(false)
    const tasks = ref(deliveryTasks.map(task => ({ ...task, selected: false })))

    const sortOptions = [
      { label: '按时间排序', value: 'time' },
      { label: '按距离排序', value: 'distance' },
      { label: '超时任务优先', value: 'overdue' }
    ]

    // 计算属性
    const filteredTasks = computed(() => {
      return tasks.value.filter(task => {
        if (currentStatus.value === 'pending') {
          return task.status === 'pending'
        } else if (currentStatus.value === 'completed') {
          return task.status === 'completed'
        }
        return true
      })
    })

    const pendingCount = computed(() => {
      return tasks.value.filter(task => task.status === 'pending').length
    })

    const completedCount = computed(() => {
      return tasks.value.filter(task => task.status === 'completed').length
    })
    // 下拉刷新
    const onRefresh = () => {
      refreshing.value = true
      setTimeout(() => {
        // 模拟刷新数据
        tasks.value = [...deliveryTasks]
        refreshing.value = false
      }, 1500)
    }

    const onRefreshRestore = () => {
      refreshing.value = false
    }

    // 方法
    const switchStatus = (status) => {
      currentStatus.value = status
    }

    const onSortChange = (e) => {
      sortIndex.value = e.detail.value
      console.log('排序方式:', sortOptions[sortIndex.value].label)
    }

    const goToSearch = () => {
      uni.navigateTo({
        url: '/pages/search/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const scanCode = () => {
      uni.scanCode({
        success: (res) => {
          uni.showToast({
            title: '扫码成功: ' + res.result,
            icon: 'none'
          })
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    }

    const goToTaskDetail = (taskId) => {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`,
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }

    const handleTaskAction = (task) => {
      if (task.status === 'pending') {
        uni.navigateTo({
          url: `/pages/task/delivery?id=${task.id}`,
          animationType: 'slide-in-right',
          animationDuration: 300
        })
      }
    }

    const toggleBatchManagement = () => {
      showBatchManagement.value = !showBatchManagement.value
      if (!showBatchManagement.value) {
        tasks.value.forEach(task => task.selected = false)
        allSelected.value = false
      }
    }


    const toggleSelectAll = () => {
      allSelected.value = !allSelected.value
      tasks.value.forEach(task => {
        if (currentStatus.value === 'pending' ? task.status === 'pending' : task.status === 'completed') {
          task.selected = allSelected.value
        }
      })
    }

    const batchComplete = () => {
      uni.showToast({
        title: '批量完成操作',
        icon: 'none'
      })
    }

    const batchDelete = () => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除选中的任务吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    }

    // 生命周期
    onMounted(() => {
      // 页面加载时的初始化操作
    })

    // 返回响应式数据和方法
    return {
      currentStatus,
      sortIndex,
      showBatchManagement,
      allSelected,
      refreshing,
      tasks,
      sortOptions,
      filteredTasks,
      pendingCount,
      completedCount,
      onRefresh,
      onRefreshRestore,
      switchStatus,
      onSortChange,
      goToSearch,
      scanCode,
      goToTaskDetail,
      handleTaskAction,
      toggleBatchManagement,
      toggleSelectAll,
      batchComplete,
      batchDelete
    }
  }
}
</script>

<style scoped>
/* 任务容器 */
.task-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  position: relative;
}

/* 悬浮头部区域 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--bg-white);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.refresh-btn {
  font-size: 28rpx;
}

/* 任务列表 */
.task-list {
  flex: 1;
  height: 0; /* 配合flex: 1 使用 */
  margin-top: 200rpx;  /* 为固定头部留出空间 */
  padding-top: 20rpx;
}

/* 状态标签 */
.status-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

/* 筛选与排序 */
.filter-sort-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.filter-options {
  flex: 1;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  font-size: 26rpx;
}

.arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  padding: 12rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 12rpx;
  transition: all 0.3s ease;
  border: 1rpx solid transparent;
}

.search-box:active {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

.search-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: var(--text-gray);
}

.search-placeholder {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 任务列表 */
.task-list {
  flex: 1;
}

.task-item {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
  padding: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-id {
  font-size: 26rpx;
  color: var(--text-gray);
}

.task-status {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.task-status.status-pending {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.task-status.status-completed {
  background-color: var(--success-color);
  color: var(--text-white);
}

.task-content {
  margin-bottom: 20rpx;
}

.task-address {
  display: flex;
  margin-bottom: 15rpx;
}

.task-address .label {
  font-size: 26rpx;
  color: var(--text-gray);
  margin-right: 10rpx;
  flex-shrink: 0;
}

.task-address .address {
  font-size: 26rpx;
  color: var(--text-dark);
  line-height: 1.4;
  flex: 1;
}

.task-time,
.task-customer {
  display: flex;
  margin-bottom: 10rpx;
}

.task-time .label,
.task-customer .label {
  font-size: 26rpx;
  color: var(--text-gray);
  margin-right: 10rpx;
  flex-shrink: 0;
}

.task-time .time,
.task-customer .customer {
  font-size: 26rpx;
  color: var(--text-dark);
  flex: 1;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distance {
  font-size: 24rpx;
  color: var(--text-gray);
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.action-delivery {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.action-btn.action-completed {
  background-color: var(--success-color);
  color: var(--text-white);
}

/* 批量管理 */
.batch-management {
  position: fixed;
  bottom: calc(120rpx + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  padding: 20rpx 30rpx;
  border-top: 1rpx solid var(--border-color);
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selectAll {
  display: flex;
  align-items: center;
}

.selectAll text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
}

.batch-btn.delete {
  background-color: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 30rpx;
  color: var(--text-gray);
}

/* 下拉刷新 */
.refresh-control {
  position: absolute;
  top: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
}
</style>
