<template>
  <view class="task-container">
    <!-- 悬浮头部区域 -->
    <view class="fixed-header">
      <!-- 页面头部 -->
      <view class="header">
        <text class="header-title">{{ taskType === 'pickup' ? '取件列表' : '派件列表' }}</text>
      </view>

      <!-- 状态切换标签 -->
      <view class="status-tabs">
        <view
          class="tab-item"
          :class="{ active: currentStatus === 'pending' }"
          @click="switchStatus('pending')">
          {{ taskType === 'pickup' ? '待取件' : '待派件' }}
        </view>
        <view
          class="tab-item"
          :class="{ active: currentStatus === 'completed' }"
          @click="switchStatus('completed')">
          {{ taskType === 'pickup' ? '已取件' : '已签收' }}
        </view>
        <view
          class="tab-item"
          :class="{ active: currentStatus === 'cancelled' }"
          @click="switchStatus('cancelled')"
          v-if="taskType === 'pickup'">
          已取消
        </view>
      </view>

      <!-- 筛选与排序 -->
      <view class="filter-sort-container">
        <view class="filter-options">
          <picker @change="onSortChange" :value="sortIndex" :range="sortOptions" range-key="label">
            <view class="filter-btn">
              <text>排序: {{ sortOptions[sortIndex].label }}</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </view>
        <view class="search-box" @click="toggleSearch">
          <text class="search-icon">🔍</text>
          <text class="search-placeholder">搜索任务</text>
        </view>
      </view>
    </view>

    <!-- 任务列表 -->
    <scroll-view
      class="task-list"
      scroll-y="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRefreshRestore"
      refresher-enabled="true"
      show-scrollbar="false">
      <view class="task-item" v-for="task in filteredTasks" :key="task.id" @click="goToTaskDetail(task.id)">
        <view class="task-header">
          <text class="task-id">任务单号: {{ task.id }}</text>
          <text class="task-status" :class="task.statusClass">{{ task.statusText }}</text>
        </view>
        <view class="task-content">
          <view class="task-address" v-if="taskType === 'pickup'">
            <text class="label">寄件地址:</text>
            <text class="address">{{ task.pickupAddress }}</text>
          </view>
          <view class="task-address" v-else>
            <text class="label">收货地址:</text>
            <text class="address">{{ task.deliveryAddress }}</text>
          </view>
          <view class="task-time">
            <text class="label">{{ taskType === 'pickup' ? '预约时间:' : '派送时间:' }}</text>
            <text class="time">{{ taskType === 'pickup' ? task.pickupTime : task.deliveryTime }}</text>
          </view>
          <view class="task-customer" v-if="taskType === 'pickup'">
            <text class="label">寄件人:</text>
            <text class="customer">{{ task.senderName }} {{ task.senderPhone }}</text>
          </view>
          <view class="task-customer" v-else>
            <text class="label">收货人:</text>
            <text class="customer">{{ task.customerName }} {{ task.customerPhone }}</text>
          </view>
        </view>
        <view class="task-footer">
          <text class="distance">{{ task.distance }}km</text>
          <button class="action-btn" :class="task.actionClass" @click.stop="handleTaskAction(task)">{{ task.actionText }}</button>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredTasks.length === 0">
        <text class="empty-text">暂无任务</text>
      </view>
    </scroll-view>




  </view>
</template>

<script>
import { pickupTasks, deliveryTasks, getTasksByStatus, TASK_STATUS } from '@/utils/mockData.js'

export default {
  props: {
    taskType: {
      type: String,
      default: 'pickup' // pickup 或 delivery
    }
  },
  data() {
    return {
      currentStatus: 'pending', // 当前状态: pending(待处理), completed(已完成), cancelled(已取消)
      sortIndex: 0, // 当前排序索引

      refreshing: false, // 下拉刷新状态
      sortOptions: [
        { label: '按时间排序', value: 'time' },
        { label: '按距离排序', value: 'distance' },
        { label: '超时任务优先', value: 'overdue' }
      ],
      pickupTasks: pickupTasks,
      deliveryTasks: deliveryTasks
    }
  },
  computed: {
    filteredTasks() {
      // 根据任务类型和当前状态过滤任务
      const tasks = this.taskType === 'pickup' ? this.pickupTasks : this.deliveryTasks;

      return tasks.filter(task => {
        if (this.currentStatus === 'pending') {
          return task.status === 'pending';
        } else if (this.currentStatus === 'completed') {
          return task.status === 'completed';
        } else if (this.currentStatus === 'cancelled') {
          return task.status === 'cancelled';
        }
        return true;
      });
    }
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      // 模拟刷新数据
      setTimeout(() => {
        // 重新加载任务数据
        this.loadTasks();
        this.refreshing = false;
      }, 1500);
    },

    onRefreshRestore() {
      this.refreshing = false;
    },

    // 加载任务数据
    loadTasks() {
      // 这里可以添加实际的数据加载逻辑
      // 目前使用模拟数据
      this.pickupTasks = [...pickupTasks];
      this.deliveryTasks = [...deliveryTasks];
    },

    switchStatus(status) {
      this.currentStatus = status;
    },
    onSortChange(e) {
      this.sortIndex = e.detail.value;
      // 实际项目中这里会重新排序任务列表
      console.log('排序方式:', this.sortOptions[this.sortIndex].label);
    },

    goToTaskDetail(taskId) {
      // 跳转到任务详情页
      if (!taskId) {
        uni.showToast({
          title: '任务ID无效',
          icon: 'none'
        });
        return;
      }

      uni.navigateTo({
        url: `/pages/task/detail?id=${encodeURIComponent(taskId)}`,
        fail: (err) => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
          console.error('导航失败:', err);
        }
      });
    },
    handleTaskAction(task) {
      // 处理任务操作
      if (this.taskType === 'pickup' && task.status === 'pending') {
        // 跳转到去取件页面
        uni.navigateTo({
          url: `/pages/task/pickup?id=${task.id}`
        });
      } else if (this.taskType === 'delivery' && task.status === 'pending') {
        // 跳转到去派件页面
        uni.navigateTo({
          url: `/pages/task/delivery?id=${task.id}`
        });
      }
    },
    toggleBatchManagement() {
      this.showBatchManagement = !this.showBatchManagement;
      if (!this.showBatchManagement) {
        // 退出批量管理时重置选择状态
        const tasks = this.taskType === 'pickup' ? this.pickupTasks : this.deliveryTasks;
        tasks.forEach(task => task.selected = false);
        this.allSelected = false;
      }
    },
    toggleSelectAll() {
      this.allSelected = !this.allSelected;
      this.filteredTasks.forEach(task => task.selected = this.allSelected);
    },
    batchComplete() {
      // 批量完成操作
      uni.showToast({
        title: '批量完成操作',
        icon: 'none'
      });
    },
    batchDelete() {
      // 批量删除操作
      uni.showModal({
        title: '确认删除',
        content: '确定要删除选中的任务吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            // 实际项目中这里会执行删除操作
          }
        }
      });
    },
    toggleSearch() {
      // 切换搜索框显示
      console.log('切换搜索框显示');
    }
  }
}
</script>

<style>
/* 任务容器 */
.task-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  position: relative;
}

/* 悬浮头部区域 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--bg-white);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 30rpx 20rpx 30rpx;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}



/* 状态标签 */
.status-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

/* 任务列表 */
.task-list {
  flex: 1;
  margin-top: 200rpx;  /* 为固定头部留出空间 */
  padding-top: 20rpx;
}

/* 筛选与排序 */
.filter-sort-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.filter-options {
  flex: 1;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  font-size: 26rpx;
}

.arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  padding: 12rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 12rpx;
  transition: all 0.3s ease;
  border: 1rpx solid transparent;
}

.search-box:active {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

.search-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: var(--text-gray);
}

.search-placeholder {
  font-size: 28rpx;
  color: var(--text-gray);
}



.task-item {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
  padding: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.task-id {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.task-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  background-color: var(--bg-gray);
  color: var(--text-white);
}

.task-status.status-pending {
  background-color: #f39c12;
}

.task-status.status-completed {
  background-color: #27ae60;
}

.task-status.status-cancelled {
  background-color: #95a5a6;
}

.task-content {
  margin-bottom: 20rpx;
}

.label {
  font-size: 24rpx;
  color: var(--text-gray);
  margin-right: 10rpx;
}

.address, .time, .customer {
  font-size: 26rpx;
  color: var(--text-dark);
  margin-bottom: 10rpx;
  display: block;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distance {
  font-size: 24rpx;
  color: var(--primary-color);
}

.action-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}

.action-btn.action-pickup {
  background-color: var(--accent-color);
}

.action-btn.action-delivery {
  background-color: #3498db;
}

.action-btn.action-completed {
  background-color: var(--bg-gray);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 批量管理 */
.batch-management {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-color);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selectAll {
  display: flex;
  align-items: center;
}

.selectAll text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}

.batch-btn.delete {
  background-color: var(--text-error);
}

/* 管理按钮 */
.management-btn {
  position: fixed;
  bottom: calc(120rpx + env(safe-area-inset-bottom));
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-primary);
}
</style>
