<template>
  <view class="main-container" :class="{ 'page-enter-active': pageLoaded }">
    <!-- 顶部导航栏 -->
    <view class="header">
      <text class="header-title">工作台</text>
    </view>

    <!-- 页面内容 -->
    <scroll-view scroll-y="true" class="page-content">
      <view class="home-container">
        <!-- 核心任务概览 -->
        <view class="task-overview">
          <view class="task-card" @click="goToTaskList('pickup')">
            <text class="card-title">取件任务</text>
            <text class="card-value">{{ taskStats.pickup }}</text>
          </view>
          <view class="task-card" @click="goToTaskList('delivery')">
            <text class="card-title">派件任务</text>
            <text class="card-value">{{ taskStats.delivery }}</text>
          </view>
          <view class="task-card" @click="goToTaskList('overdue')">
            <text class="card-title">超时任务</text>
            <text class="card-value">{{ taskStats.overdue }}</text>
          </view>
        </view>

        <!-- 滚动消息栏 -->
        <view class="message-scroll">
          <text class="message-icon">📢</text>
          <swiper class="message-swiper" vertical autoplay interval="3000" circular>
            <swiper-item>
              <text class="message-text">您有一个新的取件订单，请及时处理</text>
            </swiper-item>
            <swiper-item>
              <text class="message-text">订单SF1234567890即将超时，请尽快派送</text>
            </swiper-item>
            <swiper-item>
              <text class="message-text">客户张女士催促派送订单SF0987654321</text>
            </swiper-item>
          </swiper>
        </view>

        <!-- 常用功能区 -->
        <view class="quick-actions">
          <view class="action-item" @click="scanCode">
            <text class="action-icon">📷</text>
            <text class="action-label">签收扫描</text>
          </view>
          <view class="action-item" @click="goToAllTasks">
            <text class="action-icon">📋</text>
            <text class="action-label">全部取派</text>
          </view>
          <view class="action-item" @click="goToSignNotice">
            <text class="action-icon">🔔</text>
            <text class="action-label">签收提醒</text>
          </view>
          <view class="action-item" @click="goToFreightQuery">
            <text class="action-icon">💰</text>
            <text class="action-label">运费查询</text>
          </view>
          <view class="action-item" @click="goToExclusivePickup">
            <text class="action-icon">📦</text>
            <text class="action-label">专属取寄</text>
          </view>
        </view>

        <!-- 今日数据统计 -->
        <view class="today-stats">
          <view class="stat-item">
            <text class="stat-label">今日已取</text>
            <text class="stat-value">{{ todayStats.picked }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">今日已签</text>
            <text class="stat-value">{{ todayStats.signed }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { statistics } from '@/utils/mockData.js'

export default {
  name: 'MainPage',
  data() {
    return {
      // 页面加载完成标志，用于触发动画
      pageLoaded: false,
      // 任务统计数据
      taskStats: statistics.today,
      // 今日统计数据
      todayStats: {
        picked: statistics.today.picked,
        signed: statistics.today.signed
      }
    }
  },
  mounted() {
    // 页面加载完成后触发动画
    this.$nextTick(() => {
      this.pageLoaded = true;
    });
  },
  methods: {

    /**
     * 跳转到任务列表页面
     * @param {string} filter - 任务筛选条件
     */
    goToTaskList(filter) {
      if (filter === 'pickup') {
        uni.switchTab({
          url: '/pages/task/list'
        })
      } else if (filter === 'delivery') {
        uni.switchTab({
          url: '/pages/task/delivery-list'
        })
      } else {
        uni.navigateTo({
          url: `/pages/task/list?filter=${filter}`
        })
      }
    },

    /**
     * 跳转到全部取派页面
     */
    goToAllTasks() {
      uni.navigateTo({
        url: '/pages/task/all'
      })
    },

    /**
     * 跳转到签收提醒页面
     */
    goToSignNotice() {
      uni.navigateTo({
        url: '/pages/message/index?tab=system&subTab=sign'
      })
    },

    /**
     * 跳转到运费查询页面
     */
    goToFreightQuery() {
      uni.navigateTo({
        url: '/pages/freight/index'
      })
    },

    /**
     * 跳转到专属取寄页面
     */
    goToExclusivePickup() {
      uni.navigateTo({
        url: '/pages/exclusive/index'
      })
    },

    /**
     * 调用扫码功能
     */
    scanCode() {
      uni.scanCode({
        success: (res) => {
          uni.showToast({
            title: '扫码成功: ' + res.result,
            icon: 'none'
          })
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.main-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-top: max(env(safe-area-inset-top), 40rpx);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.5s ease-out;
}

.main-container.page-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3498db;
  color: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.page-content {
  flex: 1;
  height: 100%;
}

/* 首页样式 */
.home-container {
  padding: 20rpx;
}

/* 任务概览 */
.task-overview {
  display: flex;
  background-color: var(--bg-white);
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.task-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.task-card:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

.card-title {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 消息滚动栏 */
.message-scroll {
  display: flex;
  align-items: center;
  background-color: var(--bg-white);
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.message-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: var(--primary-color);
}

.message-swiper {
  flex: 1;
  height: 50rpx;
}

.message-text {
  font-size: 26rpx;
  color: var(--text-dark);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 快捷功能区 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  background-color: var(--bg-white);
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.action-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.action-item:nth-child(n+6) {
  margin-bottom: 0;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 15rpx;
}

.action-label {
  font-size: 24rpx;
  color: var(--text-dark);
}

/* 今日统计数据 */
.today-stats {
  display: flex;
  background-color: var(--bg-white);
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}


</style>
