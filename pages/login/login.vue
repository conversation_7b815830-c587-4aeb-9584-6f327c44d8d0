<template>
  <!-- 登录页面容器 -->
  <view class="login-container" ref="loginContainer">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>

    <!-- 登录页面头部 -->
    <view class="login-header">
      <!-- 应用Logo -->
      <image class="logo" src="/static/logo.png"></image>
      <!-- 应用标题 -->
      <text class="app-title">快递员端</text>
    </view>

    <!-- 登录方式切换标签 -->
    <view class="login-tabs">
      <view
        :class="{ active: activeTab === 'account' }"
        @click="switchToAccount"
        class="tab-item"
      >
        账号登录
      </view>
      <view
        :class="{ active: activeTab === 'phone' }"
        @click="switchToPhone"
        class="tab-item"
      >
        手机号登录
      </view>
    </view>

    <!-- 账号登录表单 -->
    <view v-if="activeTab === 'account'" class="login-form">
      <!-- 用户名输入框组 -->
      <view class="input-group">
        <input
          type="text"
          v-model="account.username"
          placeholder="请输入账号"
          class="login-input"
        />
      </view>

      <!-- 密码输入框组 -->
      <view class="input-group password-group">
        <input
          :type="showPassword ? 'text' : 'password'"
          v-model="account.password"
          placeholder="请输入密码"
          class="login-input"
          @keyup.enter="handleAccountLogin"
        />
        <!-- 密码可见性切换图标 -->
        <view
          @click="togglePasswordVisibility"
          :class="['eye-icon', { closed: !showPassword }]"
        ></view>
      </view>

      <!-- 登录按钮 -->
      <button
        :disabled="!isValidAccountLogin"
        @click="handleAccountLogin"
        class="login-button"
      >
        登录
      </button>

      <!-- 账号登录错误信息 -->
      <text v-if="accountError" class="error-message">{{ accountError }}</text>
    </view>

    <!-- 手机号登录表单 -->
    <view v-else class="login-form">
      <!-- 手机号输入框组 -->
      <view class="input-group">
        <input
          type="text"
          v-model="phone.phoneNumber"
          placeholder="请输入手机号"
          maxlength="11"
          class="login-input"
        />
      </view>

      <!-- 验证码输入框组 -->
      <view class="input-group verification-group">
        <input
          type="text"
          v-model="phone.code"
          placeholder="请输入验证码"
          maxlength="6"
          class="login-input verification-input"
        />
        <!-- 获取验证码按钮 -->
        <button
          :disabled="countdown > 0"
          @click="getCode"
          class="verification-button"
        >
          {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
        </button>
      </view>

      <!-- 登录按钮 -->
      <button
        :disabled="!isValidPhoneLogin"
        @click="handlePhoneLogin"
        class="login-button"
      >
        登录
      </button>

      <!-- 手机号登录错误信息 -->
      <text v-if="phoneError" class="error-message">{{ phoneError }}</text>
    </view>


  </view>
</template>

<script>
// 导入登录服务和验证工具
import { LoginService, ValidationUtil } from './login.js';

export default {
  // 页面数据
  data() {
    return {
      activeTab: 'account', // 当前激活的登录标签页 ('account' 或 'phone')
      showPassword: false,  // 密码是否可见
      countdown: 0,         // 验证码倒计时
      // 账号登录表单数据
      account: {
        username: '',
        password: ''
      },
      // 手机号登录表单数据
      phone: {
        phoneNumber: '',
        code: ''
      },
      accountError: '',     // 账号登录错误信息
      phoneError: '',       // 手机号登录错误信息
      animationData: {}     // 登录动画数据
    }
  },
  // 计算属性
  computed: {
    // 验证账号登录表单是否有效
    isValidAccountLogin() {
      return this.account.username && this.account.password
    },
    // 验证手机号登录表单是否有效
    isValidPhoneLogin() {
      return /^1[3-9]\d{9}$/.test(this.phone.phoneNumber) && /^\d{6}$/.test(this.phone.code)
    }
  },
  /**
     * 页面加载时执行
     */
    created() {
      try {
        // 获取当前主题设置
        this.currentTheme = uni.getStorageSync('theme') || 'system';
      } catch (e) {
        console.error('Theme initialization failed:', e);
        this.currentTheme = 'system';
      }
    },
  // 页面方法
  methods: {
    /**
     * 切换到账号登录标签页 - 清空手机号登录表单
     */
    switchToAccount() {
      // 清空手机号登录表单数据和错误信息
      this.phone = {
        phoneNumber: '',
        code: ''
      };
      this.phoneError = '';
      this.activeTab = 'account';
    },

    /**
     * 切换到手机号登录标签页 - 清空账号登录表单
     */
    switchToPhone() {
      // 清空账号登录表单数据和错误信息
      this.account = {
        username: '',
        password: ''
      };
      this.accountError = '';
      this.activeTab = 'phone';
    },

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },

    /**
     * 处理账号登录
     */
    handleAccountLogin() {
      // 账号登录处理
      if (!this.isValidAccountLogin) return;

      // 显示登录loading
      uni.showLoading({
        title: '登录中...'
      });

      // 模拟登录请求
      setTimeout(() => {
        uni.hideLoading();

        // 检查账号密码是否为测试账号
        if (this.account.username === 'test' && this.account.password === '123456') {
          // 登录成功，跳转到主页
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          });

          // 延迟跳转以确保提示显示
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }, 1000);
        } else {
          // 登录失败
          this.accountError = '账号或密码错误';
        }
      }, 1000);
    },

    /**
       * 获取验证码
       */
    async getCode() {
      // 检查手机号是否为空
      if (!this.phone.phoneNumber) {
        this.phoneError = '请输入手机号';
        return;
      }

      // 验证手机号格式 - 使用与isValidPhoneLogin一致的严格规则
      if (!/^1[3-9]\d{9}$/.test(this.phone.phoneNumber)) {
        this.phoneError = '请输入正确的手机号';
        return;
      }

      // 清除错误信息
      this.phoneError = '';

      try {
        // 显示加载提示
        uni.showLoading({
          title: '发送中...'
        });

        // 调用发送验证码服务
      const result = await LoginService.sendVerificationCode(this.phone.phoneNumber);

        // 隐藏加载提示
        uni.hideLoading();

        // 显示成功提示
        uni.showToast({
          title: result.message,
          icon: 'success'
        });

        // 启动倒计时
        this.startCountdown();
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误信息
        this.phoneError = error.message;
      }
    },

    /**
     * 启动验证码倒计时
     */
    startCountdown() {
      this.countdown = 60;
      const timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(timer);
        }
      }, 1000);
    },

    /**
     * 处理手机号登录
     */
    async handlePhoneLogin() {
      // 手机号登录处理
      if (!this.isValidPhoneLogin) return;

      try {
        // 显示加载提示
        uni.showLoading({
          title: '登录中...'
        });

        // 调用手机号登录服务
        const result = await LoginService.phoneLogin(
          this.phone.phoneNumber,
          this.phone.code
        );

        // 隐藏加载提示
        uni.hideLoading();

        // 登录成功，跳转到主页
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 延迟跳转以确保提示显示
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }, 1000);
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误信息
        this.phoneError = error.message || '登录失败';
      }
    },

    // 移除原有的onThemeChange方法，使用新的switchTheme方法
  }
}
</script>

<!-- 引入登录页面样式 - 保持scoped隔离并添加主题兼容处理 -->
<style scoped src="./login.css"></style>

<!-- 全局主题变量穿透样式 - 确保主题切换能正确应用 -->
<style>
</style>
