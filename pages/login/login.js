// 判断是否为测试模式（在开发环境中使用模拟数据）
const IS_TEST_MODE = true;

/**
 * 登录功能逻辑处理模块
 * 包含登录相关的业务逻辑处理
 */

/**
 * 登录服务类
 * 提供账号登录、手机号登录和验证码发送功能
 */
class LoginService {
  
  /**
   * 通用的请求处理方法
   * 根据环境决定使用真实请求还是模拟请求
   * @param {Object} options - 请求配置参数
   * @returns {Promise} 请求结果Promise
   */
  static request(options) {
    // 在测试模式下，使用模拟请求
    if (IS_TEST_MODE) {
      return this.mockRequest(options);
    }
    
    return new Promise((resolve, reject) => {
      // 检查网络连接
      uni.getNetworkType({
        success: function(res) {
          if (res.networkType === 'none') {
            reject({
              success: false,
              message: '当前无网络连接'
            });
            return;
          }
          
          // 设置默认请求配置
          const requestOptions = {
            ...options,
            header: {
              ...(options.header || {}),
              'content-type': 'application/json',
              'Authorization': `Bearer ${uni.getStorageSync('token') || ''}`
            },
            success: (res) => {
              if (res.statusCode >= 200 && res.statusCode < 300) {
                resolve(res.data);
              } else {
                reject({
                  success: false,
                  message: `请求失败，状态码：${res.statusCode}`,
                  statusCode: res.statusCode
                });
              }
            },
            fail: (err) => {
              reject({
                success: false,
                message: '网络请求失败',
                error: err
              });
            }
          };
          
          // 发起请求
          uni.request(requestOptions);
        },
        fail: (err) => {
          reject({
            success: false,
            message: '网络状态检测失败',
            error: err
          });
        }
      });
    });
  }
  
  /**
   * 模拟请求处理方法（测试模式）
   * 模拟网络请求，用于开发和测试环境
   * @param {Object} options - 请求配置参数
   * @returns {Promise} 模拟请求结果Promise
   */
  static mockRequest(options) {
    return new Promise((resolve, reject) => {
      // 模拟网络延迟（1秒）
      setTimeout(() => {
        // 模拟账号登录请求处理
        if (options.url === '/api/login/account' && options.method === 'POST') {
          const { username, password } = options.data;
          
          // 模拟账号登录验证
          // 测试账号: 用户名 'test'，密码 '123456'
          if (username === 'test' && password === '123456') {
            resolve({
              success: true,
              message: '登录成功',
              data: {
                userId: '1001',
                username: username,
                token: 'fake_token_string'
              }
            });
          } else {
            reject({
              success: false,
              message: '用户名或密码错误'
            });
          }
        } 
        // 模拟手机号登录请求处理
        else if (options.url === '/api/login/phone' && options.method === 'POST') {
          const { phoneNumber, code } = options.data;
          
          // 模拟手机号登录验证
          // 测试手机号: '***********'，验证码 '123456'
          if (phoneNumber === '***********' && code === '123456') {
            resolve({
              success: true,
              message: '登录成功',
              data: {
                userId: '1002',
                phoneNumber: phoneNumber,
                token: 'fake_token_string'
              }
            });
          } else {
            reject({
              success: false,
              message: '手机号或验证码错误'
            });
          }
        } 
        // 模拟发送验证码请求处理
        else if (options.url === '/api/login/send-code' && options.method === 'POST') {
          const { phoneNumber } = options.data;
          
          // 模拟验证码发送验证
          if (/^1\d{10}$/.test(phoneNumber)) {
            resolve({
              success: true,
              message: '验证码已发送'
            });
          } else {
            reject({
              success: false,
              message: '手机号格式不正确'
            });
          }
        } 
        // 其他请求默认成功
        else {
          resolve({
            success: true,
            message: '请求成功'
          });
        }
      }, 1000);
    });
  }
  
  /**
   * 账号密码登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise} 登录结果Promise
   */
  static accountLogin(username, password) {
    // 使用通用请求方法
    return this.request({
      url: '/api/login/account',
      method: 'POST',
      data: {
        username,
        password
      }
    });
  }

  /**
   * 手机号验证码登录
   * @param {string} phoneNumber - 手机号
   * @param {string} code - 验证码
   * @returns {Promise} 登录结果Promise
   */
  static phoneLogin(phoneNumber, code) {
    // 使用通用请求方法
    return this.request({
      url: '/api/login/phone',
      method: 'POST',
      data: {
        phoneNumber,
        code
      }
    });
  }

  /**
   * 发送验证码
   * @param {string} phoneNumber - 手机号
   * @returns {Promise} 发送结果Promise
   */
  static sendVerificationCode(phoneNumber) {
    // 使用通用请求方法
    return this.request({
      url: '/api/login/send-code',
      method: 'POST',
      data: {
        phoneNumber
      }
    });
  }
}

/**
 * 表单验证工具类
 * 提供各种表单字段的验证方法
 */
class ValidationUtil {
  /**
   * 验证手机号格式
   * @param {string} phoneNumber - 手机号
   * @returns {boolean} 是否有效
   */
  static isValidPhoneNumber(phoneNumber) {
    // 使用正则表达式验证手机号格式（以1开头的11位数字）
    return /^1\d{10}$/.test(phoneNumber);
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @returns {boolean} 是否有效
   */
  static isValidPassword(password) {
    // 在测试模式下，允许简单密码（至少1位字符）
    if (IS_TEST_MODE) {
      return password.length >= 1;
    }
    // 正式环境中要求密码包含大小写字母和数字，长度6-20
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,20}$/.test(password);
  }

  /**
   * 验证验证码格式
   * @param {string} code - 验证码
   * @returns {boolean} 是否有效
   */
  static isValidVerificationCode(code) {
    // 在测试模式下，允许任意6位字符
    if (IS_TEST_MODE) {
      return code.length === 6;
    }
    // 正式环境中要求6位数字
    return /^\d{6}$/.test(code);
  }

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱
   * @returns {boolean} 是否有效
   */
  static isValidEmail(email) {
    // 使用正则表达式验证邮箱格式
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
  
  /**
   * 验证账号密码表单
   * @param {Object} formData - 表单数据 {username, password}
   * @returns {Object} 验证结果 {isValid, errors}
   */
  static validateAccountForm(formData) {
    // 存储错误信息
    const errors = [];

    // 验证用户名
    if (!formData.username) {
      errors.push('请输入用户名');
    }

    // 验证密码
    if (!formData.password) {
      errors.push('请输入密码');
    } else if (!this.isValidPassword(formData.password)) {
      errors.push('密码需包含大小写字母和数字，长度6-20');
    }

    // 返回验证结果
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 验证手机号表单
   * @param {Object} formData - 表单数据 {phoneNumber, code}
   * @returns {Object} 验证结果 {isValid, errors}
   */
  static validatePhoneForm(formData) {
    // 存储错误信息
    const errors = [];

    // 验证手机号
    if (!formData.phoneNumber) {
      errors.push('请输入手机号');
    } else if (!this.isValidPhoneNumber(formData.phoneNumber)) {
      errors.push('手机号格式不正确');
    }

    // 验证验证码
    if (!formData.code) {
      errors.push('请输入验证码');
    } else if (!this.isValidVerificationCode(formData.code)) {
      errors.push('验证码格式不正确');
    }

    // 返回验证结果
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

// 登录模块通用方法
export function validateForm(formData) {
  if (!formData.username) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    });
    return false;
  }
  if (!formData.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    });
    return false;
  }
  return true;
}

export function handleLogin(formData) {
  // 验证表单数据
  if (validateForm(formData)) {
    // 在测试模式下，使用虚拟数据进行登录验证
    if (IS_TEST_MODE) {
      const { username, password } = formData;

      // 模拟登录验证
      if (username === 'test' && password === '123456') {
        // 模拟登录成功
        uni.setStorageSync('token', 'fake_token_string');
        uni.switchTab({
          url: '/pages/index/index'
        });
      } else {
        // 模拟登录失败
        uni.showToast({
          title: '用户名或密码错误',
          icon: 'none'
        });
      }
    } else {
      // 真实环境下的登录请求（暂时保留）
      uni.request({
        url: '/api/login',
        method: 'POST',
        data: formData,
        success(res) {
          if (res.statusCode === 200) {
            uni.setStorageSync('token', res.data.token);
            uni.switchTab({
              url: '/pages/index/index'
            });
          } else {
            uni.showToast({
              title: res.data.message || '登录失败',
              icon: 'none'
            });
          }
        }
      });
    }
  }
}

// 导出登录服务类和验证工具类
export {
  LoginService,
  ValidationUtil
};
