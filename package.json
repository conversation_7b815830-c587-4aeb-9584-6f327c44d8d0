{"name": "sl-express-courier", "version": "1.0.0", "description": "快递员端移动应用 - 基于uni-app开发的快递员工作管理系统", "main": "main.js", "scripts": {"dev": "uni build --watch", "build": "uni build", "build:app-plus": "uni build --platform app-plus", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin", "build:mp-alipay": "uni build --platform mp-alipay", "build:mp-baidu": "uni build --platform mp-baidu", "build:mp-toutiao": "uni build --platform mp-toutiao", "build:quickapp": "uni build --platform quickapp", "serve": "uni serve", "lint": "eslint --ext .js,.vue src", "test": "jest"}, "keywords": ["uni-app", "vue3", "快递员", "移动应用", "物流管理"], "author": "SL Express Team", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-app-plus": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-h5": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-mp-alipay": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-mp-baidu": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-mp-qq": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-mp-toutiao": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-quickapp": "^3.0.0-alpha-3080920230817002", "vue": "^3.3.4", "vuex": "^4.0.2"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^3.0.0-alpha-3080920230817002", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-3080920230817002", "@dcloudio/webpack-uni-mp-loader": "^3.0.0-alpha-3080920230817002", "@vue/compiler-sfc": "^3.3.4", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "jest": "^29.6.1", "sass": "^1.64.1", "sass-loader": "^13.3.2"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "repository": {"type": "git", "url": "https://github.com/your-org/sl-express-courier.git"}, "bugs": {"url": "https://github.com/your-org/sl-express-courier/issues"}, "homepage": "https://github.com/your-org/sl-express-courier#readme"}