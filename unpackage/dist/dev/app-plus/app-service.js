if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$g = {
    name: "LaunchPage",
    data() {
      return {
        // 控制动画开始的标志
        startAnimation: false
      };
    },
    mounted() {
      this.startAnimation = true;
      setTimeout(() => {
        this.navigateToLogin();
      }, 3e3);
    },
    methods: {
      /**
       * 跳转到登录页面
       */
      navigateToLogin() {
        uni.redirectTo({
          url: "/pages/login/login",
          animationType: "slide-in-right",
          animationDuration: 500
        });
      }
    }
  };
  function _sfc_render$f(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 启动页容器 "),
        vue.createElementVNode("view", { class: "launch-container" }, [
          vue.createCommentVNode(" Logo动画容器 "),
          vue.createElementVNode("view", { class: "logo-container" }, [
            vue.createCommentVNode(" 应用Logo "),
            vue.createElementVNode(
              "image",
              {
                class: vue.normalizeClass(["app-logo", { "logo-animation": $data.startAnimation }]),
                src: _imports_0,
                mode: "aspectFit"
              },
              null,
              2
              /* CLASS */
            ),
            vue.createCommentVNode(" 应用名称 "),
            vue.createElementVNode(
              "text",
              {
                class: vue.normalizeClass(["app-name", { "name-animation": $data.startAnimation }])
              },
              "快递员端",
              2
              /* CLASS */
            ),
            vue.createCommentVNode(" 加载指示器 "),
            vue.createElementVNode("view", { class: "loading-dots" }, [
              vue.createElementVNode(
                "text",
                {
                  class: vue.normalizeClass(["dot", { "dot-animation": $data.startAnimation }]),
                  style: { animationDelay: "0ms" }
                },
                ".",
                2
                /* CLASS */
              ),
              vue.createElementVNode(
                "text",
                {
                  class: vue.normalizeClass(["dot", { "dot-animation": $data.startAnimation }]),
                  style: { animationDelay: "200ms" }
                },
                ".",
                2
                /* CLASS */
              ),
              vue.createElementVNode(
                "text",
                {
                  class: vue.normalizeClass(["dot", { "dot-animation": $data.startAnimation }]),
                  style: { animationDelay: "400ms" }
                },
                ".",
                2
                /* CLASS */
              )
            ])
          ])
        ])
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesIndexLaunch = /* @__PURE__ */ _export_sfc(_sfc_main$g, [["render", _sfc_render$f], ["__scopeId", "data-v-8d8376b8"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/index/launch.vue"]]);
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const TASK_STATUS = {
    PENDING: "pending",
    IN_PROGRESS: "in_progress",
    COMPLETED: "completed",
    CANCELLED: "cancelled",
    OVERDUE: "overdue"
  };
  const TASK_TYPE = {
    PICKUP: "pickup",
    DELIVERY: "delivery"
  };
  const pickupTasks = [
    {
      id: "T20240917001",
      type: TASK_TYPE.PICKUP,
      status: TASK_STATUS.PENDING,
      statusText: "待取件",
      statusClass: "status-pending",
      senderName: "李女士",
      senderPhone: "139****9999",
      pickupAddress: "北京市海淀区中关村大街1号科技大厦202室",
      pickupTime: "10:00-12:00",
      distance: "2.5",
      actionText: "去取件",
      actionClass: "action-pickup",
      selected: false,
      weight: "2.5kg",
      itemName: "文件资料",
      paymentMethod: "寄付",
      amount: 15
    },
    {
      id: "T20240917002",
      type: TASK_TYPE.PICKUP,
      status: TASK_STATUS.PENDING,
      statusText: "待取件",
      statusClass: "status-pending",
      senderName: "王先生",
      senderPhone: "138****8888",
      pickupAddress: "北京市朝阳区建国门外大街88号商务中心501室",
      pickupTime: "14:00-16:00",
      distance: "5.2",
      actionText: "去取件",
      actionClass: "action-pickup",
      selected: false,
      weight: "1.2kg",
      itemName: "电子产品",
      paymentMethod: "到付",
      amount: 25
    },
    {
      id: "T20240917003",
      type: TASK_TYPE.PICKUP,
      status: TASK_STATUS.COMPLETED,
      statusText: "已取件",
      statusClass: "status-completed",
      senderName: "赵先生",
      senderPhone: "137****7777",
      pickupAddress: "北京市西城区西单北大街12号写字楼808室",
      pickupTime: "09:00-11:00",
      distance: "1.8",
      actionText: "已完成",
      actionClass: "action-completed",
      selected: false,
      weight: "0.8kg",
      itemName: "合同文件",
      paymentMethod: "寄付",
      amount: 12
    }
  ];
  const deliveryTasks = [
    {
      id: "T20240917004",
      type: TASK_TYPE.DELIVERY,
      status: TASK_STATUS.PENDING,
      statusText: "待派送",
      statusClass: "status-pending",
      customerName: "张先生",
      customerPhone: "138****8888",
      deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
      deliveryTime: "15:00-17:00",
      distance: "3.2",
      actionText: "去派送",
      actionClass: "action-delivery",
      selected: false,
      trackingNumber: "SF1234567890",
      weight: "1.5kg",
      paymentMethod: "到付",
      amount: 20
    },
    {
      id: "T20240917005",
      type: TASK_TYPE.DELIVERY,
      status: TASK_STATUS.PENDING,
      statusText: "待派送",
      statusClass: "status-pending",
      customerName: "刘女士",
      customerPhone: "139****9999",
      deliveryAddress: "北京市海淀区某某科技园A座508室",
      deliveryTime: "16:00-18:00",
      distance: "6.7",
      actionText: "去派送",
      actionClass: "action-delivery",
      selected: false,
      trackingNumber: "SF0987654321",
      weight: "2.8kg",
      paymentMethod: "寄付",
      amount: 0
    },
    {
      id: "T20240917006",
      type: TASK_TYPE.DELIVERY,
      status: TASK_STATUS.COMPLETED,
      statusText: "已签收",
      statusClass: "status-completed",
      customerName: "陈先生",
      customerPhone: "136****6666",
      deliveryAddress: "北京市东城区某某胡同3号院",
      deliveryTime: "11:00-13:00",
      distance: "4.1",
      actionText: "已完成",
      actionClass: "action-completed",
      selected: false,
      trackingNumber: "SF1122334455",
      weight: "0.9kg",
      paymentMethod: "寄付",
      amount: 0
    }
  ];
  const messages = [
    {
      id: 1,
      type: "system",
      title: "新取件订单",
      content: "您有一个新的取件订单，请及时处理",
      time: "2024-09-17 09:30",
      read: false
    },
    {
      id: 2,
      type: "system",
      title: "订单超时提醒",
      content: "订单SF1234567890即将超时，请尽快派送",
      time: "2024-09-17 10:15",
      read: false
    },
    {
      id: 3,
      type: "customer",
      title: "客户催促",
      content: "客户张女士催促派送订单SF0987654321",
      time: "2024-09-17 11:00",
      read: true
    }
  ];
  const statistics = {
    today: {
      pickup: 8,
      delivery: 12,
      overdue: 3,
      picked: 15,
      signed: 22
    },
    thisWeek: {
      pickup: 45,
      delivery: 67,
      picked: 89,
      signed: 123
    },
    thisMonth: {
      pickup: 180,
      delivery: 245,
      picked: 356,
      signed: 489
    }
  };
  const userInfo = {
    id: "U001",
    name: "张三",
    phone: "138****8888",
    avatar: "/static/avatar.png",
    workNumber: "YG001",
    department: "北京配送中心",
    rating: 4.8,
    level: "金牌快递员",
    workArea: "海淀区中关村片区"
  };
  function getTasksByStatus(tasks, status) {
    return tasks.filter((task) => task.status === status);
  }
  const _sfc_main$f = {
    setup() {
      const unreadMessages = vue.ref(messages.filter((msg) => !msg.read).length);
      const taskStats = vue.reactive(statistics.today);
      const todayStats = vue.reactive({
        picked: statistics.today.picked,
        signed: statistics.today.signed
      });
      const messageList = vue.ref(messages);
      const pendingTasks = vue.ref([]);
      const refreshing = vue.ref(false);
      const loadPendingTasks = () => {
        const pendingPickup = getTasksByStatus(pickupTasks, TASK_STATUS.PENDING).slice(0, 2);
        const pendingDelivery = getTasksByStatus(deliveryTasks, TASK_STATUS.PENDING).slice(0, 2);
        const pickupFormatted = pendingPickup.map((task) => ({
          id: task.id,
          type: "pickup",
          customerName: task.senderName,
          customerPhone: task.senderPhone,
          address: task.pickupAddress,
          distance: task.distance,
          time: task.pickupTime
        }));
        const deliveryFormatted = pendingDelivery.map((task) => ({
          id: task.id,
          type: "delivery",
          customerName: task.customerName,
          customerPhone: task.customerPhone,
          address: task.deliveryAddress,
          distance: task.distance,
          time: task.deliveryTime
        }));
        pendingTasks.value = [...pickupFormatted, ...deliveryFormatted];
      };
      const onRefresh = () => {
        refreshing.value = true;
        setTimeout(() => {
          loadPendingTasks();
          taskStats.pickup = pickupTasks.filter((task) => task.status === "pending").length;
          taskStats.delivery = deliveryTasks.filter((task) => task.status === "pending").length;
          taskStats.overdue = [...pickupTasks, ...deliveryTasks].filter((task) => task.isOverdue).length;
          refreshing.value = false;
        }, 1500);
      };
      const onRefreshRestore = () => {
        refreshing.value = false;
      };
      const goToSearch = () => {
        uni.navigateTo({
          url: "/pages/search/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToMessageCenter = () => {
        uni.navigateTo({
          url: "/pages/message/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToTaskList = (filter) => {
        if (filter === "pickup") {
          uni.switchTab({
            url: "/pages/task/list"
          });
        } else if (filter === "delivery") {
          uni.switchTab({
            url: "/pages/task/delivery-list"
          });
        } else if (filter === "overdue") {
          uni.navigateTo({
            url: "/pages/task/list?overdue=true",
            animationType: "slide-in-right",
            animationDuration: 300
          });
        }
      };
      const goToMessageDetail = (messageId) => {
        formatAppLog("log", "at pages/index/index.vue:268", "跳转到消息详情，ID:", messageId);
      };
      const scanCode = () => {
        uni.scanCode({
          success: (res) => {
            uni.showToast({
              title: "扫码成功: " + res.result,
              icon: "none"
            });
          },
          fail: () => {
            uni.showToast({
              title: "扫码失败",
              icon: "none"
            });
          }
        });
      };
      const goToAllTasks = () => {
        uni.navigateTo({
          url: "/pages/task/all",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToSignNotice = () => {
        uni.navigateTo({
          url: "/pages/message/index?tab=system&subTab=sign",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToFreightQuery = () => {
        uni.navigateTo({
          url: "/pages/freight/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToExclusivePickup = () => {
        uni.navigateTo({
          url: "/pages/exclusive/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToTaskDetail = (taskId) => {
        uni.navigateTo({
          url: "/pages/task/detail?id=" + taskId,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const callCustomer = (phone) => {
        uni.makePhoneCall({
          phoneNumber: phone
        });
      };
      const sendMessage = (phone) => {
        uni.showToast({
          title: "发送短信功能开发中",
          icon: "none"
        });
      };
      const cancelTask = (taskId) => {
        uni.showModal({
          title: "取消任务",
          content: "确定要取消该任务吗？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "任务已取消",
                icon: "success"
              });
            }
          }
        });
      };
      const goToPickup = () => {
        uni.switchTab({
          url: "/pages/task/list"
        });
      };
      const goToDelivery = () => {
        uni.switchTab({
          url: "/pages/task/delivery-list"
        });
      };
      const goToProfile = () => {
        uni.switchTab({
          url: "/pages/profile/index"
        });
      };
      vue.onMounted(() => {
        loadPendingTasks();
      });
      return {
        unreadMessages,
        taskStats,
        todayStats,
        messages: messageList,
        pendingTasks,
        refreshing,
        loadPendingTasks,
        onRefresh,
        onRefreshRestore,
        goToSearch,
        goToMessageCenter,
        goToTaskList,
        goToMessageDetail,
        scanCode,
        goToAllTasks,
        goToSignNotice,
        goToFreightQuery,
        goToExclusivePickup,
        goToTaskDetail,
        callCustomer,
        sendMessage,
        cancelTask,
        goToPickup,
        goToDelivery,
        goToProfile
      };
    }
  };
  function _sfc_render$e(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 首页容器 "),
        vue.createElementVNode("scroll-view", {
          class: "home-container",
          "scroll-y": "true",
          "refresher-enabled": "true",
          "refresher-triggered": $setup.refreshing,
          onRefresherrefresh: _cache[11] || (_cache[11] = (...args) => $setup.onRefresh && $setup.onRefresh(...args)),
          onRefresherrestore: _cache[12] || (_cache[12] = (...args) => $setup.onRefreshRestore && $setup.onRefreshRestore(...args)),
          "refresher-default-style": "black",
          "refresher-background": "#f8f9fa",
          enhanced: "true",
          "show-scrollbar": "false",
          style: { "z-index": 1001 }
        }, [
          vue.createCommentVNode(" 状态栏占位 "),
          vue.createElementVNode("view", { class: "status-bar" }),
          vue.createCommentVNode(" 顶部搜索栏 "),
          vue.createElementVNode("view", {
            class: "search-bar",
            onClick: _cache[1] || (_cache[1] = (...args) => $setup.goToSearch && $setup.goToSearch(...args))
          }, [
            vue.createElementVNode("text", { class: "search-icon" }, "🔍"),
            vue.createElementVNode("text", { class: "search-placeholder" }, "输入运单号/手机号/姓名关键词"),
            vue.createElementVNode("view", { class: "search-actions" }, [
              vue.createElementVNode("text", {
                class: "message-icon",
                onClick: _cache[0] || (_cache[0] = vue.withModifiers((...args) => $setup.goToMessageCenter && $setup.goToMessageCenter(...args), ["stop"]))
              }, "✉️"),
              $setup.unreadMessages > 0 ? (vue.openBlock(), vue.createElementBlock(
                "text",
                {
                  key: 0,
                  class: "unread-badge"
                },
                vue.toDisplayString($setup.unreadMessages > 99 ? "99+" : $setup.unreadMessages),
                1
                /* TEXT */
              )) : vue.createCommentVNode("v-if", true)
            ])
          ]),
          vue.createCommentVNode(" 核心任务概览 "),
          vue.createElementVNode(
            "view",
            {
              class: "task-overview",
              style: vue.normalizeStyle({ "margin-top": "calc(env(safe-area-inset-top) + 120rpx)" })
            },
            [
              vue.createElementVNode("view", {
                class: "task-card",
                onClick: _cache[2] || (_cache[2] = ($event) => $setup.goToTaskList("pickup"))
              }, [
                vue.createElementVNode("text", { class: "card-title" }, "取件任务"),
                vue.createElementVNode(
                  "text",
                  { class: "card-value" },
                  vue.toDisplayString($setup.taskStats.pickup),
                  1
                  /* TEXT */
                )
              ]),
              vue.createElementVNode("view", {
                class: "task-card",
                onClick: _cache[3] || (_cache[3] = ($event) => $setup.goToTaskList("delivery"))
              }, [
                vue.createElementVNode("text", { class: "card-title" }, "派件任务"),
                vue.createElementVNode(
                  "text",
                  { class: "card-value" },
                  vue.toDisplayString($setup.taskStats.delivery),
                  1
                  /* TEXT */
                )
              ]),
              vue.createElementVNode("view", {
                class: "task-card",
                onClick: _cache[4] || (_cache[4] = ($event) => $setup.goToTaskList("overdue"))
              }, [
                vue.createElementVNode("text", { class: "card-title" }, "超时任务"),
                vue.createElementVNode(
                  "text",
                  { class: "card-value" },
                  vue.toDisplayString($setup.taskStats.overdue),
                  1
                  /* TEXT */
                )
              ])
            ],
            4
            /* STYLE */
          ),
          vue.createCommentVNode(" 滚动消息栏 "),
          vue.createElementVNode("view", { class: "message-scroll" }, [
            vue.createElementVNode("text", { class: "message-icon" }, "📢"),
            vue.createElementVNode("swiper", {
              class: "message-swiper",
              vertical: "",
              autoplay: "",
              interval: "3000",
              circular: ""
            }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.messages, (message, index) => {
                  return vue.openBlock(), vue.createElementBlock("swiper-item", {
                    key: index,
                    onClick: ($event) => $setup.goToMessageDetail(message.id)
                  }, [
                    vue.createElementVNode(
                      "text",
                      { class: "message-text" },
                      vue.toDisplayString(message.content),
                      1
                      /* TEXT */
                    )
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ])
          ]),
          vue.createCommentVNode(" 常用功能区 "),
          vue.createElementVNode("view", { class: "quick-actions" }, [
            vue.createElementVNode("view", {
              class: "action-item",
              onClick: _cache[5] || (_cache[5] = (...args) => $setup.scanCode && $setup.scanCode(...args))
            }, [
              vue.createElementVNode("text", { class: "action-icon" }, "📷"),
              vue.createElementVNode("text", { class: "action-label" }, "签收扫描")
            ]),
            vue.createElementVNode("view", {
              class: "action-item",
              onClick: _cache[6] || (_cache[6] = (...args) => $setup.goToAllTasks && $setup.goToAllTasks(...args))
            }, [
              vue.createElementVNode("text", { class: "action-icon" }, "📋"),
              vue.createElementVNode("text", { class: "action-label" }, "全部取派")
            ]),
            vue.createElementVNode("view", {
              class: "action-item",
              onClick: _cache[7] || (_cache[7] = (...args) => $setup.goToSignNotice && $setup.goToSignNotice(...args))
            }, [
              vue.createElementVNode("text", { class: "action-icon" }, "🔔"),
              vue.createElementVNode("text", { class: "action-label" }, "签收提醒")
            ]),
            vue.createElementVNode("view", {
              class: "action-item",
              onClick: _cache[8] || (_cache[8] = (...args) => $setup.goToFreightQuery && $setup.goToFreightQuery(...args))
            }, [
              vue.createElementVNode("text", { class: "action-icon" }, "💰"),
              vue.createElementVNode("text", { class: "action-label" }, "运费查询")
            ]),
            vue.createElementVNode("view", {
              class: "action-item",
              onClick: _cache[9] || (_cache[9] = (...args) => $setup.goToExclusivePickup && $setup.goToExclusivePickup(...args))
            }, [
              vue.createElementVNode("text", { class: "action-icon" }, "📦"),
              vue.createElementVNode("text", { class: "action-label" }, "专属取寄")
            ])
          ]),
          vue.createCommentVNode(" 今日数据统计 "),
          vue.createElementVNode("view", { class: "today-stats" }, [
            vue.createElementVNode("view", { class: "stat-item" }, [
              vue.createElementVNode("text", { class: "stat-label" }, "今日已取"),
              vue.createElementVNode(
                "text",
                { class: "stat-value" },
                vue.toDisplayString($setup.todayStats.picked),
                1
                /* TEXT */
              )
            ]),
            vue.createElementVNode("view", { class: "stat-item" }, [
              vue.createElementVNode("text", { class: "stat-label" }, "今日已签"),
              vue.createElementVNode(
                "text",
                { class: "stat-value" },
                vue.toDisplayString($setup.todayStats.signed),
                1
                /* TEXT */
              )
            ])
          ]),
          vue.createCommentVNode(" 待办任务列表 "),
          vue.createElementVNode("view", { class: "pending-tasks" }, [
            vue.createElementVNode("view", { class: "section-header" }, [
              vue.createElementVNode("text", { class: "section-title" }, "待办任务"),
              vue.createElementVNode("text", {
                class: "view-all",
                onClick: _cache[10] || (_cache[10] = (...args) => $setup.goToAllTasks && $setup.goToAllTasks(...args))
              }, "查看更多")
            ]),
            vue.createElementVNode("view", { class: "task-list" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.pendingTasks, (task) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    class: "task-card",
                    key: task.id,
                    onClick: ($event) => $setup.goToTaskDetail(task.id)
                  }, [
                    vue.createCommentVNode(" 任务类型标识 "),
                    vue.createElementVNode(
                      "view",
                      {
                        class: vue.normalizeClass(["task-type-badge", task.type])
                      },
                      [
                        vue.createElementVNode(
                          "text",
                          { class: "type-icon" },
                          vue.toDisplayString(task.type === "pickup" ? "📦" : "🚚"),
                          1
                          /* TEXT */
                        ),
                        vue.createElementVNode(
                          "text",
                          { class: "type-text" },
                          vue.toDisplayString(task.type === "pickup" ? "取件" : "派件"),
                          1
                          /* TEXT */
                        )
                      ],
                      2
                      /* CLASS */
                    ),
                    vue.createCommentVNode(" 主要信息区域 "),
                    vue.createElementVNode("view", { class: "task-main-info" }, [
                      vue.createCommentVNode(" 客户信息 "),
                      vue.createElementVNode("view", { class: "customer-section" }, [
                        vue.createElementVNode("view", { class: "customer-header" }, [
                          vue.createElementVNode(
                            "text",
                            { class: "customer-name" },
                            vue.toDisplayString(task.customerName),
                            1
                            /* TEXT */
                          ),
                          vue.createElementVNode("view", { class: "distance-badge" }, [
                            vue.createElementVNode(
                              "text",
                              { class: "distance-text" },
                              vue.toDisplayString(task.distance) + "km",
                              1
                              /* TEXT */
                            )
                          ])
                        ]),
                        vue.createElementVNode(
                          "text",
                          { class: "customer-phone" },
                          vue.toDisplayString(task.customerPhone),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createCommentVNode(" 地址信息 "),
                      vue.createElementVNode("view", { class: "address-section" }, [
                        vue.createElementVNode(
                          "text",
                          { class: "address-label" },
                          vue.toDisplayString(task.type === "pickup" ? "取件地址" : "派送地址"),
                          1
                          /* TEXT */
                        ),
                        vue.createElementVNode(
                          "text",
                          { class: "address-text" },
                          vue.toDisplayString(task.address),
                          1
                          /* TEXT */
                        )
                      ]),
                      vue.createCommentVNode(" 时间信息 "),
                      vue.createElementVNode("view", { class: "time-section" }, [
                        vue.createElementVNode("text", { class: "time-label" }, "预约时间"),
                        vue.createElementVNode(
                          "text",
                          { class: "time-text" },
                          vue.toDisplayString(task.time),
                          1
                          /* TEXT */
                        )
                      ])
                    ]),
                    vue.createCommentVNode(" 操作按钮 "),
                    vue.createElementVNode("view", { class: "action-section" }, [
                      vue.createElementVNode("view", { class: "action-buttons" }, [
                        vue.createElementVNode("button", {
                          class: "action-btn call-btn",
                          onClick: vue.withModifiers(($event) => $setup.callCustomer(task.customerPhone), ["stop"])
                        }, [
                          vue.createElementVNode("text", { class: "btn-icon" }, "📞"),
                          vue.createElementVNode("text", { class: "btn-text" }, "电话")
                        ], 8, ["onClick"]),
                        vue.createElementVNode("button", {
                          class: "action-btn message-btn",
                          onClick: vue.withModifiers(($event) => $setup.sendMessage(task.customerPhone), ["stop"])
                        }, [
                          vue.createElementVNode("text", { class: "btn-icon" }, "💬"),
                          vue.createElementVNode("text", { class: "btn-text" }, "短信")
                        ], 8, ["onClick"]),
                        task.type === "pickup" ? (vue.openBlock(), vue.createElementBlock("button", {
                          key: 0,
                          class: "action-btn cancel-btn",
                          onClick: vue.withModifiers(($event) => $setup.cancelTask(task.id), ["stop"])
                        }, [
                          vue.createElementVNode("text", { class: "btn-icon" }, "❌"),
                          vue.createElementVNode("text", { class: "btn-text" }, "取消")
                        ], 8, ["onClick"])) : (vue.openBlock(), vue.createElementBlock("button", {
                          key: 1,
                          class: "action-btn deliver-btn",
                          onClick: vue.withModifiers(($event) => $setup.goToTaskDetail(task.id), ["stop"])
                        }, [
                          vue.createElementVNode("text", { class: "btn-icon" }, "✅"),
                          vue.createElementVNode("text", { class: "btn-text" }, "派送")
                        ], 8, ["onClick"]))
                      ])
                    ])
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              )),
              vue.createCommentVNode(" 空状态 "),
              $setup.pendingTasks.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "empty-state"
              }, [
                vue.createElementVNode("text", { class: "empty-icon" }, "📋"),
                vue.createElementVNode("text", { class: "empty-text" }, "暂无待办任务"),
                vue.createElementVNode("text", { class: "empty-desc" }, "您的任务列表是空的")
              ])) : vue.createCommentVNode("v-if", true)
            ])
          ])
        ], 40, ["refresher-triggered"])
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$f, [["render", _sfc_render$e], ["__scopeId", "data-v-1cf27b2a"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/index/index.vue"]]);
  class LoginService {
    /**
     * 通用的请求处理方法
     * 根据环境决定使用真实请求还是模拟请求
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 请求结果Promise
     */
    static request(options) {
      {
        return this.mockRequest(options);
      }
    }
    /**
     * 模拟请求处理方法（测试模式）
     * 模拟网络请求，用于开发和测试环境
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 模拟请求结果Promise
     */
    static mockRequest(options) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (options.url === "/api/login/account" && options.method === "POST") {
            const { username, password } = options.data;
            if (username === "test" && password === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1001",
                  username,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "用户名或密码错误"
              });
            }
          } else if (options.url === "/api/login/phone" && options.method === "POST") {
            const { phoneNumber, code } = options.data;
            if (phoneNumber === "***********" && code === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1002",
                  phoneNumber,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "手机号或验证码错误"
              });
            }
          } else if (options.url === "/api/login/send-code" && options.method === "POST") {
            const { phoneNumber } = options.data;
            if (/^1\d{10}$/.test(phoneNumber)) {
              resolve({
                success: true,
                message: "验证码已发送"
              });
            } else {
              reject({
                success: false,
                message: "手机号格式不正确"
              });
            }
          } else {
            resolve({
              success: true,
              message: "请求成功"
            });
          }
        }, 1e3);
      });
    }
    /**
     * 账号密码登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 登录结果Promise
     */
    static accountLogin(username, password) {
      return this.request({
        url: "/api/login/account",
        method: "POST",
        data: {
          username,
          password
        }
      });
    }
    /**
     * 手机号验证码登录
     * @param {string} phoneNumber - 手机号
     * @param {string} code - 验证码
     * @returns {Promise} 登录结果Promise
     */
    static phoneLogin(phoneNumber, code) {
      return this.request({
        url: "/api/login/phone",
        method: "POST",
        data: {
          phoneNumber,
          code
        }
      });
    }
    /**
     * 发送验证码
     * @param {string} phoneNumber - 手机号
     * @returns {Promise} 发送结果Promise
     */
    static sendVerificationCode(phoneNumber) {
      return this.request({
        url: "/api/login/send-code",
        method: "POST",
        data: {
          phoneNumber
        }
      });
    }
  }
  const _sfc_main$e = {
    // 页面数据
    data() {
      return {
        activeTab: "account",
        // 当前激活的登录标签页 ('account' 或 'phone')
        showPassword: false,
        // 密码是否可见
        countdown: 0,
        // 验证码倒计时
        // 账号登录表单数据
        account: {
          username: "",
          password: ""
        },
        // 手机号登录表单数据
        phone: {
          phoneNumber: "",
          code: ""
        },
        accountError: "",
        // 账号登录错误信息
        phoneError: "",
        // 手机号登录错误信息
        animationData: {}
        // 登录动画数据
      };
    },
    // 计算属性
    computed: {
      // 验证账号登录表单是否有效
      isValidAccountLogin() {
        return this.account.username && this.account.password;
      },
      // 验证手机号登录表单是否有效
      isValidPhoneLogin() {
        return /^1[3-9]\d{9}$/.test(this.phone.phoneNumber) && /^\d{6}$/.test(this.phone.code);
      }
    },
    /**
       * 页面加载时执行
       */
    created() {
      try {
        this.currentTheme = uni.getStorageSync("theme") || "system";
      } catch (e) {
        formatAppLog("error", "at pages/login/login.vue:168", "Theme initialization failed:", e);
        this.currentTheme = "system";
      }
    },
    // 页面方法
    methods: {
      /**
       * 切换到账号登录标签页 - 清空手机号登录表单
       */
      switchToAccount() {
        this.phone = {
          phoneNumber: "",
          code: ""
        };
        this.phoneError = "";
        this.activeTab = "account";
      },
      /**
       * 切换到手机号登录标签页 - 清空账号登录表单
       */
      switchToPhone() {
        this.account = {
          username: "",
          password: ""
        };
        this.accountError = "";
        this.activeTab = "phone";
      },
      /**
       * 切换密码可见性
       */
      togglePasswordVisibility() {
        this.showPassword = !this.showPassword;
      },
      /**
       * 处理账号登录
       */
      handleAccountLogin() {
        if (!this.isValidAccountLogin)
          return;
        uni.showLoading({
          title: "登录中..."
        });
        setTimeout(() => {
          uni.hideLoading();
          if (this.account.username === "test" && this.account.password === "123456") {
            uni.showToast({
              title: "登录成功",
              icon: "success"
            });
            setTimeout(() => {
              uni.switchTab({
                url: "/pages/index/index"
              });
            }, 1e3);
          } else {
            this.accountError = "账号或密码错误";
          }
        }, 1e3);
      },
      /**
         * 获取验证码
         */
      async getCode() {
        if (!this.phone.phoneNumber) {
          this.phoneError = "请输入手机号";
          return;
        }
        if (!/^1[3-9]\d{9}$/.test(this.phone.phoneNumber)) {
          this.phoneError = "请输入正确的手机号";
          return;
        }
        this.phoneError = "";
        try {
          uni.showLoading({
            title: "发送中..."
          });
          const result = await LoginService.sendVerificationCode(this.phone.phoneNumber);
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          this.startCountdown();
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message;
        }
      },
      /**
       * 启动验证码倒计时
       */
      startCountdown() {
        this.countdown = 60;
        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--;
          } else {
            clearInterval(timer);
          }
        }, 1e3);
      },
      /**
       * 处理手机号登录
       */
      async handlePhoneLogin() {
        if (!this.isValidPhoneLogin)
          return;
        try {
          uni.showLoading({
            title: "登录中..."
          });
          const result = await LoginService.phoneLogin(
            this.phone.phoneNumber,
            this.phone.code
          );
          uni.hideLoading();
          uni.showToast({
            title: "登录成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/index/index"
            });
          }, 1e3);
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message || "登录失败";
        }
      }
      // 移除原有的onThemeChange方法，使用新的switchTheme方法
    }
  };
  function _sfc_render$d(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 登录页面容器 "),
        vue.createElementVNode(
          "view",
          {
            class: "login-container",
            ref: "loginContainer"
          },
          [
            vue.createCommentVNode(" 状态栏占位 "),
            vue.createElementVNode("view", { class: "status-bar" }),
            vue.createCommentVNode(" 登录页面头部 "),
            vue.createElementVNode("view", { class: "login-header" }, [
              vue.createCommentVNode(" 应用Logo "),
              vue.createElementVNode("image", {
                class: "logo",
                src: _imports_0
              }),
              vue.createCommentVNode(" 应用标题 "),
              vue.createElementVNode("text", { class: "app-title" }, "快递员端")
            ]),
            vue.createCommentVNode(" 登录方式切换标签 "),
            vue.createElementVNode("view", { class: "login-tabs" }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass([{ active: $data.activeTab === "account" }, "tab-item"]),
                  onClick: _cache[0] || (_cache[0] = (...args) => $options.switchToAccount && $options.switchToAccount(...args))
                },
                " 账号登录 ",
                2
                /* CLASS */
              ),
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass([{ active: $data.activeTab === "phone" }, "tab-item"]),
                  onClick: _cache[1] || (_cache[1] = (...args) => $options.switchToPhone && $options.switchToPhone(...args))
                },
                " 手机号登录 ",
                2
                /* CLASS */
              )
            ]),
            vue.createCommentVNode(" 账号登录表单 "),
            $data.activeTab === "account" ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "login-form"
            }, [
              vue.createCommentVNode(" 用户名输入框组 "),
              vue.createElementVNode("view", { class: "input-group" }, [
                vue.withDirectives(vue.createElementVNode(
                  "input",
                  {
                    type: "text",
                    "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.account.username = $event),
                    placeholder: "请输入账号",
                    class: "login-input"
                  },
                  null,
                  512
                  /* NEED_PATCH */
                ), [
                  [vue.vModelText, $data.account.username]
                ])
              ]),
              vue.createCommentVNode(" 密码输入框组 "),
              vue.createElementVNode("view", { class: "input-group password-group" }, [
                vue.withDirectives(vue.createElementVNode("input", {
                  type: $data.showPassword ? "text" : "password",
                  "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.account.password = $event),
                  placeholder: "请输入密码",
                  class: "login-input",
                  onKeyup: _cache[4] || (_cache[4] = vue.withKeys((...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args), ["enter"]))
                }, null, 40, ["type"]), [
                  [vue.vModelDynamic, $data.account.password]
                ]),
                vue.createCommentVNode(" 密码可见性切换图标 "),
                vue.createElementVNode(
                  "view",
                  {
                    onClick: _cache[5] || (_cache[5] = (...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),
                    class: vue.normalizeClass(["eye-icon", { closed: !$data.showPassword }])
                  },
                  null,
                  2
                  /* CLASS */
                )
              ]),
              vue.createCommentVNode(" 登录按钮 "),
              vue.createElementVNode("button", {
                disabled: !$options.isValidAccountLogin,
                onClick: _cache[6] || (_cache[6] = (...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args)),
                class: "login-button"
              }, " 登录 ", 8, ["disabled"]),
              vue.createCommentVNode(" 账号登录错误信息 "),
              $data.accountError ? (vue.openBlock(), vue.createElementBlock(
                "text",
                {
                  key: 0,
                  class: "error-message"
                },
                vue.toDisplayString($data.accountError),
                1
                /* TEXT */
              )) : vue.createCommentVNode("v-if", true)
            ])) : (vue.openBlock(), vue.createElementBlock(
              vue.Fragment,
              { key: 1 },
              [
                vue.createCommentVNode(" 手机号登录表单 "),
                vue.createElementVNode("view", { class: "login-form" }, [
                  vue.createCommentVNode(" 手机号输入框组 "),
                  vue.createElementVNode("view", { class: "input-group" }, [
                    vue.withDirectives(vue.createElementVNode(
                      "input",
                      {
                        type: "text",
                        "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $data.phone.phoneNumber = $event),
                        placeholder: "请输入手机号",
                        maxlength: "11",
                        class: "login-input"
                      },
                      null,
                      512
                      /* NEED_PATCH */
                    ), [
                      [vue.vModelText, $data.phone.phoneNumber]
                    ])
                  ]),
                  vue.createCommentVNode(" 验证码输入框组 "),
                  vue.createElementVNode("view", { class: "input-group verification-group" }, [
                    vue.withDirectives(vue.createElementVNode(
                      "input",
                      {
                        type: "text",
                        "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.phone.code = $event),
                        placeholder: "请输入验证码",
                        maxlength: "6",
                        class: "login-input verification-input"
                      },
                      null,
                      512
                      /* NEED_PATCH */
                    ), [
                      [vue.vModelText, $data.phone.code]
                    ]),
                    vue.createCommentVNode(" 获取验证码按钮 "),
                    vue.createElementVNode("button", {
                      disabled: $data.countdown > 0,
                      onClick: _cache[9] || (_cache[9] = (...args) => $options.getCode && $options.getCode(...args)),
                      class: "verification-button"
                    }, vue.toDisplayString($data.countdown > 0 ? `${$data.countdown}秒后重发` : "获取验证码"), 9, ["disabled"])
                  ]),
                  vue.createCommentVNode(" 登录按钮 "),
                  vue.createElementVNode("button", {
                    disabled: !$options.isValidPhoneLogin,
                    onClick: _cache[10] || (_cache[10] = (...args) => $options.handlePhoneLogin && $options.handlePhoneLogin(...args)),
                    class: "login-button"
                  }, " 登录 ", 8, ["disabled"]),
                  vue.createCommentVNode(" 手机号登录错误信息 "),
                  $data.phoneError ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "error-message"
                    },
                    vue.toDisplayString($data.phoneError),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ])
              ],
              2112
              /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
            ))
          ],
          512
          /* NEED_PATCH */
        )
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$e, [["render", _sfc_render$d], ["__scopeId", "data-v-e4e4508d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/login/login.vue"]]);
  const _sfc_main$d = {
    props: {
      taskType: {
        type: String,
        default: "pickup"
        // pickup 或 delivery
      }
    },
    data() {
      return {
        currentStatus: "pending",
        // 当前状态: pending(待处理), completed(已完成), cancelled(已取消)
        sortIndex: 0,
        // 当前排序索引
        refreshing: false,
        // 下拉刷新状态
        sortOptions: [
          { label: "按时间排序", value: "time" },
          { label: "按距离排序", value: "distance" },
          { label: "超时任务优先", value: "overdue" }
        ],
        pickupTasks,
        deliveryTasks
      };
    },
    computed: {
      filteredTasks() {
        const tasks = this.taskType === "pickup" ? this.pickupTasks : this.deliveryTasks;
        return tasks.filter((task) => {
          if (this.currentStatus === "pending") {
            return task.status === "pending";
          } else if (this.currentStatus === "completed") {
            return task.status === "completed";
          } else if (this.currentStatus === "cancelled") {
            return task.status === "cancelled";
          }
          return true;
        });
      }
    },
    methods: {
      // 下拉刷新
      onRefresh() {
        this.refreshing = true;
        setTimeout(() => {
          this.loadTasks();
          this.refreshing = false;
        }, 1500);
      },
      onRefreshRestore() {
        this.refreshing = false;
      },
      // 加载任务数据
      loadTasks() {
        this.pickupTasks = [...pickupTasks];
        this.deliveryTasks = [...deliveryTasks];
      },
      switchStatus(status) {
        this.currentStatus = status;
      },
      onSortChange(e) {
        this.sortIndex = e.detail.value;
        formatAppLog("log", "at pages/task/list.vue:179", "排序方式:", this.sortOptions[this.sortIndex].label);
      },
      goToTaskDetail(taskId) {
        if (!taskId) {
          uni.showToast({
            title: "任务ID无效",
            icon: "none"
          });
          return;
        }
        uni.navigateTo({
          url: `/pages/task/detail?id=${encodeURIComponent(taskId)}`,
          fail: (err) => {
            uni.showToast({
              title: "页面跳转失败",
              icon: "none"
            });
            formatAppLog("error", "at pages/task/list.vue:199", "导航失败:", err);
          }
        });
      },
      handleTaskAction(task) {
        if (this.taskType === "pickup" && task.status === "pending") {
          uni.navigateTo({
            url: `/pages/task/pickup?id=${task.id}`
          });
        } else if (this.taskType === "delivery" && task.status === "pending") {
          uni.navigateTo({
            url: `/pages/task/delivery?id=${task.id}`
          });
        }
      },
      toggleBatchManagement() {
        this.showBatchManagement = !this.showBatchManagement;
        if (!this.showBatchManagement) {
          const tasks = this.taskType === "pickup" ? this.pickupTasks : this.deliveryTasks;
          tasks.forEach((task) => task.selected = false);
          this.allSelected = false;
        }
      },
      toggleSelectAll() {
        this.allSelected = !this.allSelected;
        this.filteredTasks.forEach((task) => task.selected = this.allSelected);
      },
      batchComplete() {
        uni.showToast({
          title: "批量完成操作",
          icon: "none"
        });
      },
      batchDelete() {
        uni.showModal({
          title: "确认删除",
          content: "确定要删除选中的任务吗？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        });
      },
      toggleSearch() {
        formatAppLog("log", "at pages/task/list.vue:255", "切换搜索框显示");
      }
    }
  };
  function _sfc_render$c(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "task-container" }, [
      vue.createCommentVNode(" 悬浮头部区域 "),
      vue.createElementVNode("view", { class: "fixed-header" }, [
        vue.createCommentVNode(" 页面头部 "),
        vue.createElementVNode("view", { class: "header" }, [
          vue.createElementVNode(
            "text",
            { class: "header-title" },
            vue.toDisplayString($props.taskType === "pickup" ? "取件列表" : "派件列表"),
            1
            /* TEXT */
          )
        ]),
        vue.createCommentVNode(" 状态切换标签 "),
        vue.createElementVNode("view", { class: "status-tabs" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentStatus === "pending" }]),
              onClick: _cache[0] || (_cache[0] = ($event) => $options.switchStatus("pending"))
            },
            vue.toDisplayString($props.taskType === "pickup" ? "待取件" : "待派件"),
            3
            /* TEXT, CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentStatus === "completed" }]),
              onClick: _cache[1] || (_cache[1] = ($event) => $options.switchStatus("completed"))
            },
            vue.toDisplayString($props.taskType === "pickup" ? "已取件" : "已签收"),
            3
            /* TEXT, CLASS */
          ),
          $props.taskType === "pickup" ? (vue.openBlock(), vue.createElementBlock(
            "view",
            {
              key: 0,
              class: vue.normalizeClass(["tab-item", { active: $data.currentStatus === "cancelled" }]),
              onClick: _cache[2] || (_cache[2] = ($event) => $options.switchStatus("cancelled"))
            },
            " 已取消 ",
            2
            /* CLASS */
          )) : vue.createCommentVNode("v-if", true)
        ]),
        vue.createCommentVNode(" 筛选与排序 "),
        vue.createElementVNode("view", { class: "filter-sort-container" }, [
          vue.createElementVNode("view", { class: "filter-options" }, [
            vue.createElementVNode("picker", {
              onChange: _cache[3] || (_cache[3] = (...args) => $options.onSortChange && $options.onSortChange(...args)),
              value: $data.sortIndex,
              range: $data.sortOptions,
              "range-key": "label"
            }, [
              vue.createElementVNode("view", { class: "filter-btn" }, [
                vue.createElementVNode(
                  "text",
                  null,
                  "排序: " + vue.toDisplayString($data.sortOptions[$data.sortIndex].label),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("text", { class: "arrow" }, "▼")
              ])
            ], 40, ["value", "range"])
          ]),
          vue.createElementVNode("view", {
            class: "search-box",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.toggleSearch && $options.toggleSearch(...args))
          }, [
            vue.createElementVNode("text", { class: "search-icon" }, "🔍"),
            vue.createElementVNode("text", { class: "search-placeholder" }, "搜索任务")
          ])
        ])
      ]),
      vue.createCommentVNode(" 任务列表 "),
      vue.createElementVNode("scroll-view", {
        class: "task-list",
        "scroll-y": "true",
        "refresher-enabled": "true",
        "refresher-triggered": $data.refreshing,
        onRefresherrefresh: _cache[5] || (_cache[5] = (...args) => $options.onRefresh && $options.onRefresh(...args)),
        onRefresherrestore: _cache[6] || (_cache[6] = (...args) => $options.onRefreshRestore && $options.onRefreshRestore(...args)),
        "refresher-default-style": "black",
        "refresher-background": "#f8f9fa",
        enhanced: "true",
        "show-scrollbar": "false"
      }, [
        (vue.openBlock(true), vue.createElementBlock(
          vue.Fragment,
          null,
          vue.renderList($options.filteredTasks, (task) => {
            return vue.openBlock(), vue.createElementBlock("view", {
              class: "task-item",
              key: task.id,
              onClick: ($event) => $options.goToTaskDetail(task.id)
            }, [
              vue.createElementVNode("view", { class: "task-header" }, [
                vue.createElementVNode(
                  "text",
                  { class: "task-id" },
                  "任务单号: " + vue.toDisplayString(task.id),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  {
                    class: vue.normalizeClass(["task-status", task.statusClass])
                  },
                  vue.toDisplayString(task.statusText),
                  3
                  /* TEXT, CLASS */
                )
              ]),
              vue.createElementVNode("view", { class: "task-content" }, [
                $props.taskType === "pickup" ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "task-address"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "寄件地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.pickupAddress),
                    1
                    /* TEXT */
                  )
                ])) : (vue.openBlock(), vue.createElementBlock("view", {
                  key: 1,
                  class: "task-address"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "收货地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.deliveryAddress),
                    1
                    /* TEXT */
                  )
                ])),
                vue.createElementVNode("view", { class: "task-time" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "label" },
                    vue.toDisplayString($props.taskType === "pickup" ? "预约时间:" : "派送时间:"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "time" },
                    vue.toDisplayString($props.taskType === "pickup" ? task.pickupTime : task.deliveryTime),
                    1
                    /* TEXT */
                  )
                ]),
                $props.taskType === "pickup" ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 2,
                  class: "task-customer"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "寄件人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.senderName) + " " + vue.toDisplayString(task.senderPhone),
                    1
                    /* TEXT */
                  )
                ])) : (vue.openBlock(), vue.createElementBlock("view", {
                  key: 3,
                  class: "task-customer"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "收货人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.customerName) + " " + vue.toDisplayString(task.customerPhone),
                    1
                    /* TEXT */
                  )
                ]))
              ]),
              vue.createElementVNode("view", { class: "task-footer" }, [
                vue.createElementVNode(
                  "text",
                  { class: "distance" },
                  vue.toDisplayString(task.distance) + "km",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("button", {
                  class: vue.normalizeClass(["action-btn", task.actionClass]),
                  onClick: vue.withModifiers(($event) => $options.handleTaskAction(task), ["stop"])
                }, vue.toDisplayString(task.actionText), 11, ["onClick"])
              ])
            ], 8, ["onClick"]);
          }),
          128
          /* KEYED_FRAGMENT */
        )),
        vue.createCommentVNode(" 空状态 "),
        $options.filteredTasks.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无任务")
        ])) : vue.createCommentVNode("v-if", true)
      ], 40, ["refresher-triggered"])
    ]);
  }
  const PagesTaskList = /* @__PURE__ */ _export_sfc(_sfc_main$d, [["render", _sfc_render$c], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/list.vue"]]);
  const _sfc_main$c = {
    setup() {
      const currentStatus = vue.ref("pending");
      const sortIndex = vue.ref(0);
      const showBatchManagement = vue.ref(false);
      const allSelected = vue.ref(false);
      const refreshing = vue.ref(false);
      const tasks = vue.ref(deliveryTasks.map((task) => ({ ...task, selected: false })));
      const sortOptions = [
        { label: "按时间排序", value: "time" },
        { label: "按距离排序", value: "distance" },
        { label: "超时任务优先", value: "overdue" }
      ];
      const filteredTasks = vue.computed(() => {
        return tasks.value.filter((task) => {
          if (currentStatus.value === "pending") {
            return task.status === "pending";
          } else if (currentStatus.value === "completed") {
            return task.status === "completed";
          }
          return true;
        });
      });
      const pendingCount = vue.computed(() => {
        return tasks.value.filter((task) => task.status === "pending").length;
      });
      const completedCount = vue.computed(() => {
        return tasks.value.filter((task) => task.status === "completed").length;
      });
      const onRefresh = () => {
        refreshing.value = true;
        setTimeout(() => {
          tasks.value = [...deliveryTasks];
          refreshing.value = false;
        }, 1500);
      };
      const onRefreshRestore = () => {
        refreshing.value = false;
      };
      const switchStatus = (status) => {
        currentStatus.value = status;
      };
      const onSortChange = (e) => {
        sortIndex.value = e.detail.value;
        formatAppLog("log", "at pages/task/delivery-list.vue:148", "排序方式:", sortOptions[sortIndex.value].label);
      };
      const goToSearch = () => {
        uni.navigateTo({
          url: "/pages/search/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const scanCode = () => {
        uni.scanCode({
          success: (res) => {
            uni.showToast({
              title: "扫码成功: " + res.result,
              icon: "none"
            });
          },
          fail: () => {
            uni.showToast({
              title: "扫码失败",
              icon: "none"
            });
          }
        });
      };
      const goToTaskDetail = (taskId) => {
        uni.navigateTo({
          url: `/pages/task/detail?id=${taskId}`,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const handleTaskAction = (task) => {
        if (task.status === "pending") {
          uni.navigateTo({
            url: `/pages/task/delivery?id=${task.id}`,
            animationType: "slide-in-right",
            animationDuration: 300
          });
        }
      };
      const toggleBatchManagement = () => {
        showBatchManagement.value = !showBatchManagement.value;
        if (!showBatchManagement.value) {
          tasks.value.forEach((task) => task.selected = false);
          allSelected.value = false;
        }
      };
      const goBack = () => {
        uni.navigateBack();
      };
      const toggleSelectAll = () => {
        allSelected.value = !allSelected.value;
        tasks.value.forEach((task) => {
          if (currentStatus.value === "pending" ? task.status === "pending" : task.status === "completed") {
            task.selected = allSelected.value;
          }
        });
      };
      const batchComplete = () => {
        uni.showToast({
          title: "批量完成操作",
          icon: "none"
        });
      };
      const batchDelete = () => {
        uni.showModal({
          title: "确认删除",
          content: "确定要删除选中的任务吗？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        });
      };
      vue.onMounted(() => {
      });
      return {
        currentStatus,
        sortIndex,
        showBatchManagement,
        allSelected,
        refreshing,
        tasks,
        sortOptions,
        filteredTasks,
        pendingCount,
        completedCount,
        onRefresh,
        onRefreshRestore,
        switchStatus,
        onSortChange,
        goToSearch,
        scanCode,
        goToTaskDetail,
        handleTaskAction,
        toggleBatchManagement,
        goBack,
        toggleSelectAll,
        batchComplete,
        batchDelete
      };
    }
  };
  function _sfc_render$b(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "task-container" }, [
      vue.createCommentVNode(" 悬浮头部区域 "),
      vue.createElementVNode("view", { class: "fixed-header" }, [
        vue.createCommentVNode(" 页面头部 "),
        vue.createElementVNode("view", { class: "header" }, [
          vue.createElementVNode("text", { class: "header-title" }, "派件列表")
        ]),
        vue.createCommentVNode(" 状态切换标签 "),
        vue.createElementVNode("view", { class: "status-tabs" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $setup.currentStatus === "pending" }]),
              onClick: _cache[0] || (_cache[0] = ($event) => $setup.switchStatus("pending"))
            },
            " 待派件 (" + vue.toDisplayString($setup.pendingCount) + ") ",
            3
            /* TEXT, CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $setup.currentStatus === "completed" }]),
              onClick: _cache[1] || (_cache[1] = ($event) => $setup.switchStatus("completed"))
            },
            " 已签收 (" + vue.toDisplayString($setup.completedCount) + ") ",
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createCommentVNode(" 筛选与排序 "),
        vue.createElementVNode("view", { class: "filter-sort-container" }, [
          vue.createElementVNode("view", { class: "filter-options" }, [
            vue.createElementVNode("picker", {
              onChange: _cache[2] || (_cache[2] = (...args) => $setup.onSortChange && $setup.onSortChange(...args)),
              value: $setup.sortIndex,
              range: $setup.sortOptions,
              "range-key": "label"
            }, [
              vue.createElementVNode("view", { class: "filter-btn" }, [
                vue.createElementVNode(
                  "text",
                  null,
                  "排序: " + vue.toDisplayString($setup.sortOptions[$setup.sortIndex].label),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("text", { class: "arrow" }, "▼")
              ])
            ], 40, ["value", "range"])
          ]),
          vue.createElementVNode("view", {
            class: "search-box",
            onClick: _cache[3] || (_cache[3] = (...args) => $setup.goToSearch && $setup.goToSearch(...args))
          }, [
            vue.createElementVNode("text", { class: "search-icon" }, "🔍"),
            vue.createElementVNode("text", { class: "search-placeholder" }, "搜索任务")
          ])
        ])
      ]),
      vue.createCommentVNode(" 任务列表 "),
      vue.createElementVNode("scroll-view", {
        class: "task-list",
        "scroll-y": "true",
        "refresher-enabled": "true",
        "refresher-triggered": $setup.refreshing,
        onRefresherrefresh: _cache[4] || (_cache[4] = (...args) => $setup.onRefresh && $setup.onRefresh(...args)),
        onRefresherrestore: _cache[5] || (_cache[5] = (...args) => $setup.onRefreshRestore && $setup.onRefreshRestore(...args)),
        enhanced: "true",
        "show-scrollbar": "false"
      }, [
        (vue.openBlock(true), vue.createElementBlock(
          vue.Fragment,
          null,
          vue.renderList($setup.filteredTasks, (task) => {
            return vue.openBlock(), vue.createElementBlock("view", {
              class: "task-item",
              key: task.id,
              onClick: ($event) => $setup.goToTaskDetail(task.id)
            }, [
              vue.createElementVNode("view", { class: "task-header" }, [
                vue.createElementVNode(
                  "text",
                  { class: "task-id" },
                  "任务单号: " + vue.toDisplayString(task.id),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  {
                    class: vue.normalizeClass(["task-status", task.statusClass])
                  },
                  vue.toDisplayString(task.statusText),
                  3
                  /* TEXT, CLASS */
                )
              ]),
              vue.createElementVNode("view", { class: "task-content" }, [
                vue.createElementVNode("view", { class: "task-address" }, [
                  vue.createElementVNode("text", { class: "label" }, "收货地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.deliveryAddress),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "task-time" }, [
                  vue.createElementVNode("text", { class: "label" }, "派送时间:"),
                  vue.createElementVNode(
                    "text",
                    { class: "time" },
                    vue.toDisplayString(task.deliveryTime),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "task-customer" }, [
                  vue.createElementVNode("text", { class: "label" }, "收货人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.customerName) + " " + vue.toDisplayString(task.customerPhone),
                    1
                    /* TEXT */
                  )
                ])
              ]),
              vue.createElementVNode("view", { class: "task-footer" }, [
                vue.createElementVNode(
                  "text",
                  { class: "distance" },
                  vue.toDisplayString(task.distance) + "km",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("button", {
                  class: vue.normalizeClass(["action-btn", task.actionClass]),
                  onClick: vue.withModifiers(($event) => $setup.handleTaskAction(task), ["stop"])
                }, vue.toDisplayString(task.actionText), 11, ["onClick"])
              ])
            ], 8, ["onClick"]);
          }),
          128
          /* KEYED_FRAGMENT */
        )),
        vue.createCommentVNode(" 空状态 "),
        $setup.filteredTasks.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无任务")
        ])) : vue.createCommentVNode("v-if", true)
      ], 40, ["refresher-triggered"])
    ]);
  }
  const PagesTaskDeliveryList = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["render", _sfc_render$b], ["__scopeId", "data-v-f4134a5b"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/delivery-list.vue"]]);
  const _sfc_main$b = {
    setup() {
      const profileData = vue.reactive({
        ...userInfo,
        position: "高级快递员",
        department: "北京朝阳分部",
        email: "<EMAIL>",
        employeeId: userInfo.workNumber,
        completedTasks: statistics.thisMonth.picked + statistics.thisMonth.signed,
        workDays: 22,
        earnings: "¥3,456"
      });
      const editProfile = () => {
        uni.navigateTo({
          url: "/pages/profile/edit",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToPage = (url) => {
        uni.navigateTo({
          url,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToStatistics = () => {
        uni.navigateTo({
          url: "/pages/statistics/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToTaskHistory = () => {
        uni.navigateTo({
          url: "/pages/task/history",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToWorkCalendar = () => {
        uni.navigateTo({
          url: "/pages/work/calendar",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToEarnings = () => {
        uni.navigateTo({
          url: "/pages/profile/earnings",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToSchedule = () => {
        uni.navigateTo({
          url: "/pages/work/schedule",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToWorkArea = () => {
        uni.navigateTo({
          url: "/pages/work/area",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToTraining = () => {
        uni.navigateTo({
          url: "/pages/training/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToFeedback = () => {
        uni.navigateTo({
          url: "/pages/feedback/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const logout = () => {
        uni.showModal({
          title: "退出登录",
          content: "确定要退出当前账号吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("token");
              uni.removeStorageSync("userInfo");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(() => {
      });
      return {
        userInfo: profileData,
        editProfile,
        goToPage,
        goToStatistics,
        goToTaskHistory,
        goToWorkCalendar,
        goToEarnings,
        goToSchedule,
        goToWorkArea,
        goToTraining,
        goToFeedback,
        logout
      };
    }
  };
  function _sfc_render$a(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "profile-container" }, [
      vue.createCommentVNode(" 悬浮用户信息头部 "),
      vue.createElementVNode("view", { class: "fixed-profile-header" }, [
        vue.createCommentVNode(" 用户信息卡片 "),
        vue.createElementVNode("view", { class: "profile-header" }, [
          vue.createElementVNode("view", { class: "background-gradient" }),
          vue.createElementVNode("view", { class: "user-card" }, [
            vue.createElementVNode("view", { class: "user-avatar-section" }, [
              vue.createElementVNode("view", { class: "avatar-container" }, [
                vue.createElementVNode("image", {
                  class: "avatar",
                  src: _imports_0,
                  mode: "aspectFill"
                }),
                vue.createElementVNode("view", { class: "avatar-ring" })
              ]),
              vue.createElementVNode("view", { class: "status-indicator online" })
            ]),
            vue.createElementVNode("view", { class: "user-info" }, [
              vue.createElementVNode(
                "text",
                { class: "user-name" },
                vue.toDisplayString($setup.userInfo.name),
                1
                /* TEXT */
              ),
              vue.createElementVNode("view", { class: "user-title-container" }, [
                vue.createElementVNode(
                  "text",
                  { class: "user-title" },
                  vue.toDisplayString($setup.userInfo.position),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("view", { class: "vip-badge" }, [
                  vue.createElementVNode("text", { class: "vip-text" }, "VIP")
                ])
              ]),
              vue.createElementVNode("view", { class: "user-meta" }, [
                vue.createElementVNode("view", { class: "meta-item" }, [
                  vue.createElementVNode("text", { class: "meta-icon" }, "🆔"),
                  vue.createElementVNode(
                    "text",
                    { class: "meta-text" },
                    vue.toDisplayString($setup.userInfo.employeeId),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "meta-item" }, [
                  vue.createElementVNode("text", { class: "meta-icon" }, "🏢"),
                  vue.createElementVNode(
                    "text",
                    { class: "meta-text" },
                    vue.toDisplayString($setup.userInfo.department),
                    1
                    /* TEXT */
                  )
                ])
              ])
            ]),
            vue.createElementVNode("view", { class: "header-actions" }, [
              vue.createElementVNode("view", {
                class: "action-btn",
                onClick: _cache[0] || (_cache[0] = (...args) => $setup.editProfile && $setup.editProfile(...args))
              }, [
                vue.createElementVNode("text", { class: "action-icon" }, "✏️")
              ]),
              vue.createElementVNode("view", {
                class: "action-btn",
                onClick: _cache[1] || (_cache[1] = ($event) => $setup.goToPage("/pages/profile/settings"))
              }, [
                vue.createElementVNode("text", { class: "action-icon" }, "⚙️")
              ])
            ])
          ])
        ])
      ]),
      vue.createCommentVNode(" 页面内容区域 "),
      vue.createElementVNode("view", { class: "profile-content" }, [
        vue.createCommentVNode(" 工作统计 "),
        vue.createElementVNode("view", { class: "stats-section" }, [
          vue.createElementVNode("view", { class: "section-header" }, [
            vue.createElementVNode("view", { class: "section-title" }, [
              vue.createElementVNode("text", { class: "title-text" }, "📊 工作统计"),
              vue.createElementVNode("text", { class: "title-desc" }, "本月数据概览")
            ]),
            vue.createElementVNode("view", {
              class: "view-more",
              onClick: _cache[2] || (_cache[2] = (...args) => $setup.goToStatistics && $setup.goToStatistics(...args))
            }, [
              vue.createElementVNode("text", { class: "more-text" }, "查看详情"),
              vue.createElementVNode("text", { class: "more-arrow" }, "→")
            ])
          ]),
          vue.createElementVNode("view", { class: "stats-container" }, [
            vue.createElementVNode("view", { class: "primary-stats" }, [
              vue.createElementVNode("view", {
                class: "primary-stat-card",
                onClick: _cache[3] || (_cache[3] = (...args) => $setup.goToEarnings && $setup.goToEarnings(...args))
              }, [
                vue.createElementVNode("view", { class: "stat-header" }, [
                  vue.createElementVNode("text", { class: "stat-icon" }, "💰"),
                  vue.createElementVNode("text", { class: "stat-label" }, "本月收入")
                ]),
                vue.createElementVNode(
                  "text",
                  { class: "stat-value primary" },
                  vue.toDisplayString($setup.userInfo.earnings),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("view", { class: "stat-footer" }, [
                  vue.createElementVNode("text", { class: "stat-trend positive" }, "+12.5%"),
                  vue.createElementVNode("text", { class: "stat-desc" }, "较上月")
                ])
              ])
            ]),
            vue.createElementVNode("view", { class: "secondary-stats" }, [
              vue.createElementVNode("view", {
                class: "stat-card compact",
                onClick: _cache[4] || (_cache[4] = (...args) => $setup.goToTaskHistory && $setup.goToTaskHistory(...args))
              }, [
                vue.createElementVNode("text", { class: "stat-icon" }, "📦"),
                vue.createElementVNode("view", { class: "stat-content" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "stat-value" },
                    vue.toDisplayString($setup.userInfo.completedTasks),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode("text", { class: "stat-label" }, "完成任务")
                ]),
                vue.createElementVNode("text", { class: "stat-trend" }, "+15")
              ]),
              vue.createElementVNode("view", {
                class: "stat-card compact",
                onClick: _cache[5] || (_cache[5] = (...args) => $setup.goToStatistics && $setup.goToStatistics(...args))
              }, [
                vue.createElementVNode("text", { class: "stat-icon" }, "⭐"),
                vue.createElementVNode("view", { class: "stat-content" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "stat-value" },
                    vue.toDisplayString($setup.userInfo.rating),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode("text", { class: "stat-label" }, "服务评分")
                ]),
                vue.createElementVNode("text", { class: "stat-trend" }, "+0.2")
              ]),
              vue.createElementVNode("view", {
                class: "stat-card compact",
                onClick: _cache[6] || (_cache[6] = (...args) => $setup.goToWorkCalendar && $setup.goToWorkCalendar(...args))
              }, [
                vue.createElementVNode("text", { class: "stat-icon" }, "📅"),
                vue.createElementVNode("view", { class: "stat-content" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "stat-value" },
                    vue.toDisplayString($setup.userInfo.workDays),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode("text", { class: "stat-label" }, "工作天数")
                ]),
                vue.createElementVNode("text", { class: "stat-trend" }, "+2")
              ])
            ])
          ])
        ]),
        vue.createCommentVNode(" 快捷功能 "),
        vue.createElementVNode("view", { class: "quick-actions" }, [
          vue.createElementVNode("view", { class: "section-header" }, [
            vue.createElementVNode("view", { class: "section-title" }, [
              vue.createElementVNode("text", { class: "title-text" }, "⚡ 快捷功能")
            ])
          ]),
          vue.createElementVNode("view", { class: "action-grid" }, [
            vue.createElementVNode("view", {
              class: "action-item modern",
              onClick: _cache[7] || (_cache[7] = (...args) => $setup.goToSchedule && $setup.goToSchedule(...args))
            }, [
              vue.createElementVNode("view", { class: "action-icon-container schedule" }, [
                vue.createElementVNode("text", { class: "action-icon" }, "📋")
              ]),
              vue.createElementVNode("text", { class: "action-text" }, "排班查看"),
              vue.createElementVNode("text", { class: "action-desc" }, "查看工作安排")
            ]),
            vue.createElementVNode("view", {
              class: "action-item modern",
              onClick: _cache[8] || (_cache[8] = (...args) => $setup.goToWorkArea && $setup.goToWorkArea(...args))
            }, [
              vue.createElementVNode("view", { class: "action-icon-container area" }, [
                vue.createElementVNode("text", { class: "action-icon" }, "🗺️")
              ]),
              vue.createElementVNode("text", { class: "action-text" }, "作业范围"),
              vue.createElementVNode("text", { class: "action-desc" }, "配送区域管理")
            ]),
            vue.createElementVNode("view", {
              class: "action-item modern",
              onClick: _cache[9] || (_cache[9] = (...args) => $setup.goToTraining && $setup.goToTraining(...args))
            }, [
              vue.createElementVNode("view", { class: "action-icon-container training" }, [
                vue.createElementVNode("text", { class: "action-icon" }, "🎓")
              ]),
              vue.createElementVNode("text", { class: "action-text" }, "培训学习"),
              vue.createElementVNode("text", { class: "action-desc" }, "技能提升")
            ]),
            vue.createElementVNode("view", {
              class: "action-item modern",
              onClick: _cache[10] || (_cache[10] = (...args) => $setup.goToFeedback && $setup.goToFeedback(...args))
            }, [
              vue.createElementVNode("view", { class: "action-icon-container feedback" }, [
                vue.createElementVNode("text", { class: "action-icon" }, "💬")
              ]),
              vue.createElementVNode("text", { class: "action-text" }, "意见反馈"),
              vue.createElementVNode("text", { class: "action-desc" }, "问题建议")
            ])
          ])
        ]),
        vue.createCommentVNode(" 设置菜单 "),
        vue.createElementVNode("view", { class: "settings-section" }, [
          vue.createElementVNode("view", { class: "section-title" }, [
            vue.createElementVNode("text", { class: "title-text" }, "设置与服务")
          ]),
          vue.createElementVNode("view", { class: "menu-group" }, [
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[11] || (_cache[11] = ($event) => $setup.goToPage("/pages/profile/wallet"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon wallet" }, "💰"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "我的钱包"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "余额、收入明细")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-value" }, "¥1,234.56"),
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ]),
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[12] || (_cache[12] = ($event) => $setup.goToPage("/pages/profile/orders"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon orders" }, "📦"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "历史订单"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "查看所有订单记录")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ]),
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[13] || (_cache[13] = ($event) => $setup.goToPage("/pages/profile/certificates"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon certificates" }, "📄"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "证件管理"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "身份证、驾驶证等")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ])
          ]),
          vue.createElementVNode("view", { class: "menu-group" }, [
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[14] || (_cache[14] = ($event) => $setup.goToPage("/pages/profile/settings"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon settings" }, "⚙️"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "系统设置"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "通知、隐私、账户")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ]),
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[15] || (_cache[15] = ($event) => $setup.goToPage("/pages/profile/help"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon help" }, "❓"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "帮助中心"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "常见问题、联系客服")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ]),
            vue.createElementVNode("view", {
              class: "menu-item",
              onClick: _cache[16] || (_cache[16] = ($event) => $setup.goToPage("/pages/profile/about"))
            }, [
              vue.createElementVNode("view", { class: "menu-left" }, [
                vue.createElementVNode("view", { class: "menu-icon about" }, "ℹ️"),
                vue.createElementVNode("view", { class: "menu-content" }, [
                  vue.createElementVNode("text", { class: "menu-title" }, "关于我们"),
                  vue.createElementVNode("text", { class: "menu-desc" }, "版本信息、用户协议")
                ])
              ]),
              vue.createElementVNode("view", { class: "menu-right" }, [
                vue.createElementVNode("text", { class: "menu-value" }, "v1.0.0"),
                vue.createElementVNode("text", { class: "menu-arrow" }, ">")
              ])
            ])
          ])
        ]),
        vue.createCommentVNode(" 退出登录 "),
        vue.createElementVNode("view", { class: "logout-section" }, [
          vue.createElementVNode("button", {
            class: "logout-btn",
            onClick: _cache[17] || (_cache[17] = (...args) => $setup.logout && $setup.logout(...args))
          }, [
            vue.createElementVNode("text", { class: "logout-icon" }, "🚪"),
            vue.createElementVNode("text", { class: "logout-text" }, "退出登录")
          ])
        ])
      ])
    ]);
  }
  const PagesProfileIndex = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["render", _sfc_render$a], ["__scopeId", "data-v-201c0da5"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/profile/index.vue"]]);
  const _sfc_main$a = {
    data() {
      return {
        themes: ["light", "dark", "system"],
        currentTheme: "system"
      };
    },
    methods: {
      switchTheme(theme) {
        this.currentTheme = theme;
        uni.setStorageSync("theme", theme);
        this.applyTheme();
      },
      applyTheme() {
        const theme = this.currentTheme;
        uni.setStorageSync("currentTheme", theme);
        uni.$emit("themeChanged", theme);
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          if (currentPage.$vm) {
            currentPage.$vm.$el.setAttribute("data-theme", theme);
          }
        }
      }
    },
    mounted() {
      const savedTheme = uni.getStorageSync("theme") || "system";
      this.switchTheme(savedTheme);
    }
  };
  function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "theme-switcher" }, [
      (vue.openBlock(true), vue.createElementBlock(
        vue.Fragment,
        null,
        vue.renderList($data.themes, (theme, index) => {
          return vue.openBlock(), vue.createElementBlock("button", {
            key: index,
            class: vue.normalizeClass(["theme-btn", { active: $data.currentTheme === theme }]),
            onClick: ($event) => $options.switchTheme(theme)
          }, vue.toDisplayString(theme), 11, ["onClick"]);
        }),
        128
        /* KEYED_FRAGMENT */
      ))
    ]);
  }
  const ThemeSwitcher = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$9], ["__scopeId", "data-v-d495b30c"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/components/ThemeSwitcher.vue"]]);
  const _sfc_main$9 = {
    components: {
      ThemeSwitcher
    },
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "pickup",
          // pending, pickup, delivery, completed
          statusText: "待取件",
          statusClass: "status-pickup",
          statusProgress: 2,
          trackingNumber: "SF1234567890",
          courierCompany: "顺丰速运",
          packageType: "文件",
          packageWeight: "0.5kg",
          remark: "请轻拿轻放，谢谢！",
          customerName: "张先生",
          customerPhone: "138****8888",
          deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
          deliveryTime: "工作日 anytime",
          senderName: "李女士",
          senderPhone: "139****9999",
          pickupAddress: "北京市海淀区某某大厦202室",
          pickupTime: "2023-04-01 10:00-12:00"
        }
      };
    },
    methods: {
      goBack() {
        uni.navigateBack({
          animationType: "slide-out-right",
          animationDuration: 300
        });
      },
      startTask() {
        uni.showModal({
          title: "提示",
          content: "确认开始处理该任务？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "任务开始处理",
                icon: "success"
              });
              this.task.status = "pickup";
              this.task.statusText = "待取件";
              this.task.statusClass = "status-pickup";
              this.task.statusProgress = 2;
            }
          }
        });
      },
      contactCustomer() {
        uni.showActionSheet({
          itemList: ["拨打电话", "发送短信"],
          success: (res) => {
            if (res.tapIndex === 0) {
              uni.makePhoneCall({
                phoneNumber: this.task.senderPhone
              });
            } else {
              uni.showToast({
                title: "短信功能开发中",
                icon: "none"
              });
            }
          }
        });
      },
      handleException() {
        uni.navigateTo({
          url: "/pages/task/exception"
        });
      },
      payShippingFee() {
        uni.navigateTo({
          url: "/pages/payment/index?amount=10.00&method=微信支付&orderId=T20230401001"
        });
      }
    }
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "task-detail-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "任务详情"),
        vue.createElementVNode("view", { class: "header-right" }, [
          vue.createElementVNode("text", { class: "more-icon" }, "⋯")
        ])
      ]),
      vue.createCommentVNode(" 任务状态 "),
      vue.createElementVNode("view", { class: "task-status" }, [
        vue.createElementVNode(
          "view",
          { class: "status-badge pending" },
          vue.toDisplayString($data.task.statusText),
          1
          /* TEXT */
        ),
        vue.createElementVNode("view", { class: "status-info" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "task-time" },
            "预约时间: " + vue.toDisplayString($data.task.pickupTime),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 客户信息 "),
      vue.createElementVNode("view", { class: "customer-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "客户信息"),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "客户姓名:"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($data.task.senderName),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "联系电话:"),
          vue.createElementVNode(
            "text",
            { class: "value phone-number" },
            vue.toDisplayString($data.task.senderPhone),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "取件地址:"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($data.task.pickupAddress),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 物品信息 "),
      vue.createElementVNode("view", { class: "package-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "物品信息"),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品类型:"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($data.task.packageType),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品重量:"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($data.task.packageWeight),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品数量:"),
          vue.createElementVNode("text", { class: "value" }, "1件")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "备注信息:"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($data.task.remark),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 费用信息 "),
      vue.createElementVNode("view", { class: "fee-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "费用信息"),
        vue.createElementVNode("view", { class: "fee-item" }, [
          vue.createElementVNode("text", { class: "label" }, "运费:"),
          vue.createElementVNode("text", { class: "value" }, "¥8.00")
        ]),
        vue.createElementVNode("view", { class: "fee-item" }, [
          vue.createElementVNode("text", { class: "label" }, "保价费:"),
          vue.createElementVNode("text", { class: "value" }, "¥2.00")
        ]),
        vue.createElementVNode("view", { class: "fee-item total" }, [
          vue.createElementVNode("text", { class: "label" }, "合计:"),
          vue.createElementVNode("text", { class: "value total-value" }, "¥10.00")
        ])
      ]),
      vue.createCommentVNode(" 操作记录 "),
      vue.createElementVNode("view", { class: "operation-records" }, [
        vue.createElementVNode("view", { class: "section-title" }, "操作记录"),
        vue.createElementVNode("view", { class: "record-item" }, [
          vue.createElementVNode("text", { class: "record-time" }, "2023-04-01 09:30"),
          vue.createElementVNode("text", { class: "record-desc" }, "订单已创建")
        ]),
        vue.createElementVNode("view", { class: "record-item" }, [
          vue.createElementVNode("text", { class: "record-time" }, "2023-04-01 09:35"),
          vue.createElementVNode("text", { class: "record-desc" }, "快递员已接单")
        ]),
        vue.createElementVNode("view", { class: "record-item" }, [
          vue.createElementVNode("text", { class: "record-time" }, "2023-04-01 09:50"),
          vue.createElementVNode("text", { class: "record-desc" }, "正在前往取件点")
        ])
      ]),
      vue.createCommentVNode(" 底部操作 "),
      vue.createElementVNode("view", { class: "bottom-actions" }, [
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.contactCustomer && $options.contactCustomer(...args))
        }, "联系客户"),
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[2] || (_cache[2] = (...args) => $options.handleException && $options.handleException(...args))
        }, "异常处理"),
        $data.task.status === "delivery" ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 0,
          class: "action-btn secondary",
          onClick: _cache[3] || (_cache[3] = (...args) => $options.payShippingFee && $options.payShippingFee(...args))
        }, "支付运费")) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesTaskDetail = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$8], ["__scopeId", "data-v-43b93a3d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/detail.vue"]]);
  const _sfc_main$8 = {
    data() {
      return {
        // 页面数据
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      confirmDelivery() {
        uni.showModal({
          title: "确认签收",
          content: "确认客户已签收？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "签收成功",
                icon: "success"
              });
              setTimeout(() => {
                uni.navigateBack();
              }, 1e3);
            }
          }
        });
      },
      reportException() {
        uni.navigateTo({
          url: "/pages/task/exception"
        });
      }
    }
  };
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "delivery-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "去派送"),
        vue.createElementVNode("view", { class: "header-right" }, [
          vue.createElementVNode("text", { class: "more-icon" }, "⋯")
        ])
      ]),
      vue.createCommentVNode(" 订单信息 "),
      vue.createElementVNode("view", { class: "order-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "订单信息"),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "订单号:"),
          vue.createElementVNode("text", { class: "value" }, "SF1234567890")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "下单时间:"),
          vue.createElementVNode("text", { class: "value" }, "2023-04-01 09:30")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "预约时间:"),
          vue.createElementVNode("text", { class: "value" }, "2023-04-01 15:00-17:00")
        ])
      ]),
      vue.createCommentVNode(" 收货信息 "),
      vue.createElementVNode("view", { class: "recipient-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "收货信息"),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "收货人:"),
          vue.createElementVNode("text", { class: "value" }, "张三")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "联系电话:"),
          vue.createElementVNode("text", { class: "value" }, "138****8888")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "收货地址:"),
          vue.createElementVNode("text", { class: "value" }, "北京市朝阳区某某街道某某小区1号楼101室")
        ])
      ]),
      vue.createCommentVNode(" 物品信息 "),
      vue.createElementVNode("view", { class: "package-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "物品信息"),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品类型:"),
          vue.createElementVNode("text", { class: "value" }, "文件")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品重量:"),
          vue.createElementVNode("text", { class: "value" }, "0.5kg")
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "物品数量:"),
          vue.createElementVNode("text", { class: "value" }, "1件")
        ])
      ]),
      vue.createCommentVNode(" 签收人选择 "),
      vue.createElementVNode("view", { class: "signatory-selection" }, [
        vue.createElementVNode("view", { class: "section-title" }, "签收人"),
        vue.createElementVNode("view", { class: "signatory-options" }, [
          vue.createElementVNode("view", { class: "option-item active" }, "本人签收"),
          vue.createElementVNode("view", { class: "option-item" }, "代收点"),
          vue.createElementVNode("view", { class: "option-item" }, "快递柜")
        ]),
        vue.createElementVNode("input", {
          class: "signatory-input",
          type: "text",
          placeholder: "请输入签收人姓名"
        })
      ]),
      vue.createCommentVNode(" 核心操作 "),
      vue.createElementVNode("view", { class: "action-section" }, [
        vue.createElementVNode("button", {
          class: "action-btn confirm-btn",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.confirmDelivery && $options.confirmDelivery(...args))
        }, "确认签收"),
        vue.createElementVNode("button", {
          class: "action-btn exception-btn",
          onClick: _cache[2] || (_cache[2] = (...args) => $options.reportException && $options.reportException(...args))
        }, "异常上报")
      ])
    ]);
  }
  const PagesTaskDelivery = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__scopeId", "data-v-f4f36846"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/delivery.vue"]]);
  const _sfc_main$7 = {
    data() {
      return {
        searchKeyword: "",
        searchHistory: ["SF1234567890", "张三", "***********"],
        hotSearches: ["今日任务", "超时订单", "紧急派送", "客户催单"],
        searchResults: []
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      clearKeyword() {
        this.searchKeyword = "";
        this.searchResults = [];
      },
      performSearch() {
        if (!this.searchKeyword.trim()) {
          uni.showToast({
            title: "请输入搜索关键词",
            icon: "none"
          });
          return;
        }
        if (!this.searchHistory.includes(this.searchKeyword)) {
          this.searchHistory.unshift(this.searchKeyword);
          if (this.searchHistory.length > 10) {
            this.searchHistory.pop();
          }
        }
        this.searchResults = [
          {
            id: "1",
            title: "订单 SF1234567890",
            type: "取件任务",
            typeClass: "pickup",
            description: "寄件人: 张三 138****8888 | 北京市朝阳区某某小区",
            time: "2023-04-01 10:30"
          },
          {
            id: "2",
            title: "订单 SF0987654321",
            type: "派件任务",
            typeClass: "delivery",
            description: "收货人: 李四 139****9999 | 北京市海淀区某某大厦",
            time: "2023-04-01 14:20"
          }
        ];
      },
      clearAllHistory() {
        uni.showModal({
          title: "清空搜索历史",
          content: "确定要清空所有搜索历史记录吗？",
          success: (res) => {
            if (res.confirm) {
              this.searchHistory = [];
              uni.showToast({
                title: "已清空",
                icon: "success"
              });
            }
          }
        });
      },
      searchFromHistory(keyword) {
        this.searchKeyword = keyword;
        this.performSearch();
      },
      searchFromHot(keyword) {
        this.searchKeyword = keyword;
        this.performSearch();
      },
      goToResultDetail(result) {
        formatAppLog("log", "at pages/search/index.vue:150", "跳转到结果详情页", result);
        uni.showToast({
          title: "跳转功能开发中",
          icon: "none"
        });
      }
    }
  };
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "search-container" }, [
      vue.createCommentVNode(" 悬浮头部 "),
      vue.createElementVNode("view", { class: "fixed-header" }, [
        vue.createCommentVNode(" 页面头部 "),
        vue.createElementVNode("view", { class: "header" }, [
          vue.createElementVNode("view", {
            class: "header-left",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
          }, [
            vue.createElementVNode("text", { class: "back-icon" }, "←"),
            vue.createElementVNode("text", { class: "back-text" }, "返回")
          ]),
          vue.createElementVNode("view", { class: "search-box" }, [
            vue.createElementVNode("text", { class: "search-icon" }, "🔍"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "search-input",
                type: "text",
                "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.searchKeyword = $event),
                placeholder: "输入运单号/手机号/姓名关键词",
                onConfirm: _cache[2] || (_cache[2] = (...args) => $options.performSearch && $options.performSearch(...args))
              },
              null,
              544
              /* NEED_HYDRATION, NEED_PATCH */
            ), [
              [vue.vModelText, $data.searchKeyword]
            ]),
            $data.searchKeyword ? (vue.openBlock(), vue.createElementBlock("text", {
              key: 0,
              class: "clear-icon",
              onClick: _cache[3] || (_cache[3] = (...args) => $options.clearKeyword && $options.clearKeyword(...args))
            }, "✕")) : vue.createCommentVNode("v-if", true)
          ]),
          vue.createElementVNode("text", {
            class: "cancel-btn",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.goBack && $options.goBack(...args))
          }, "取消")
        ])
      ]),
      vue.createCommentVNode(" 搜索结果 "),
      vue.createElementVNode("view", { class: "search-content" }, [
        $data.searchResults.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "search-results"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.searchResults, (result) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "result-item",
                key: result.id,
                onClick: ($event) => $options.goToResultDetail(result)
              }, [
                vue.createElementVNode("view", { class: "result-header" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "result-title" },
                    vue.toDisplayString(result.title),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    {
                      class: vue.normalizeClass(["result-type", result.typeClass])
                    },
                    vue.toDisplayString(result.type),
                    3
                    /* TEXT, CLASS */
                  )
                ]),
                vue.createElementVNode("view", { class: "result-content" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "result-desc" },
                    vue.toDisplayString(result.description),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "result-meta" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "result-time" },
                    vue.toDisplayString(result.time),
                    1
                    /* TEXT */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : (vue.openBlock(), vue.createElementBlock(
          vue.Fragment,
          { key: 1 },
          [
            vue.createCommentVNode(" 最近搜索 "),
            vue.createElementVNode("view", { class: "recent-searches" }, [
              vue.createElementVNode("view", { class: "section-header" }, [
                vue.createElementVNode("text", { class: "section-title" }, "最近搜索"),
                vue.createElementVNode("text", {
                  class: "clear-all",
                  onClick: _cache[5] || (_cache[5] = (...args) => $options.clearAllHistory && $options.clearAllHistory(...args))
                }, "清空历史")
              ]),
              vue.createElementVNode("view", { class: "history-list" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.searchHistory, (history, index) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      class: "history-item",
                      key: index,
                      onClick: ($event) => $options.searchFromHistory(history)
                    }, [
                      vue.createElementVNode(
                        "text",
                        { class: "history-text" },
                        vue.toDisplayString(history),
                        1
                        /* TEXT */
                      )
                    ], 8, ["onClick"]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ]),
              vue.createCommentVNode(" 热门搜索 "),
              vue.createElementVNode("view", { class: "section-header" }, [
                vue.createElementVNode("text", { class: "section-title" }, "热门搜索")
              ]),
              vue.createElementVNode("view", { class: "hot-searches" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.hotSearches, (hot, index) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      class: "hot-item",
                      key: index,
                      onClick: ($event) => $options.searchFromHot(hot)
                    }, [
                      vue.createElementVNode(
                        "text",
                        { class: "hot-text" },
                        vue.toDisplayString(hot),
                        1
                        /* TEXT */
                      )
                    ], 8, ["onClick"]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ])
          ],
          2112
          /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
        ))
      ])
    ]);
  }
  const PagesSearchIndex = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__scopeId", "data-v-2dab939d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/search/index.vue"]]);
  const _sfc_main$6 = {
    data() {
      return {
        currentTab: "system",
        // notice: 公告, system: 系统通知
        currentSubTab: "pickup",
        // pickup, delivery, sign, cancel
        messages: [
          {
            id: "1",
            title: "新的取件订单",
            content: "您有一个新的取件订单，请及时处理",
            time: "2023-04-01 10:30",
            type: "取件相关",
            typeClass: "pickup",
            read: false
          },
          {
            id: "2",
            title: "订单即将超时",
            content: "订单SF1234567890即将超时，请尽快派送",
            time: "2023-04-01 14:20",
            type: "派件相关",
            typeClass: "delivery",
            read: false
          },
          {
            id: "3",
            title: "客户催促派送",
            content: "客户张女士催促派送订单SF0987654321",
            time: "2023-04-01 15:45",
            type: "签收提醒",
            typeClass: "sign",
            read: true
          },
          {
            id: "4",
            title: "系统维护通知",
            content: "系统将于今晚00:00-02:00进行维护，届时可能影响使用",
            time: "2023-04-01 09:00",
            type: "公告",
            typeClass: "notice",
            read: true
          },
          {
            id: "5",
            title: "订单已取消",
            content: "客户李女士取消了订单SF1111111111",
            time: "2023-04-01 08:15",
            type: "快件取消",
            typeClass: "cancel",
            read: true
          }
        ]
      };
    },
    computed: {
      filteredMessages() {
        return this.messages.filter((message) => {
          if (this.currentTab === "notice") {
            return message.typeClass === "notice";
          } else {
            if (this.currentSubTab === "pickup") {
              return message.typeClass === "pickup";
            } else if (this.currentSubTab === "delivery") {
              return message.typeClass === "delivery";
            } else if (this.currentSubTab === "sign") {
              return message.typeClass === "sign";
            } else if (this.currentSubTab === "cancel") {
              return message.typeClass === "cancel";
            }
          }
          return true;
        });
      }
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      switchTab(tab) {
        this.currentTab = tab;
      },
      switchSubTab(subTab) {
        this.currentSubTab = subTab;
      },
      readMessage(message) {
        message.read = true;
        uni.showToast({
          title: "查看消息",
          icon: "none"
        });
      },
      markAllRead() {
        this.messages.forEach((message) => {
          message.read = true;
        });
        uni.showToast({
          title: "全部标记为已读",
          icon: "success"
        });
      }
    }
  };
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "message-container" }, [
      vue.createCommentVNode(" 悬浮头部 "),
      vue.createElementVNode("view", { class: "fixed-header" }, [
        vue.createCommentVNode(" 页面头部 "),
        vue.createElementVNode("view", { class: "header" }, [
          vue.createElementVNode("view", {
            class: "header-left",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
          }, [
            vue.createElementVNode("text", { class: "back-icon" }, "←"),
            vue.createElementVNode("text", { class: "back-text" }, "返回")
          ]),
          vue.createElementVNode("text", { class: "header-title" }, "消息中心"),
          vue.createElementVNode("view", { class: "header-right" }, [
            vue.createElementVNode("text", {
              class: "mark-all-read",
              onClick: _cache[1] || (_cache[1] = (...args) => $options.markAllRead && $options.markAllRead(...args))
            }, "全部已读")
          ])
        ]),
        vue.createCommentVNode(" 消息分类标签 "),
        vue.createElementVNode("view", { class: "message-tabs" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentTab === "notice" }]),
              onClick: _cache[2] || (_cache[2] = ($event) => $options.switchTab("notice"))
            },
            " 公告 ",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["tab-item", { active: $data.currentTab === "system" }]),
              onClick: _cache[3] || (_cache[3] = ($event) => $options.switchTab("system"))
            },
            " 系统通知 ",
            2
            /* CLASS */
          )
        ])
      ]),
      vue.createCommentVNode(" 页面内容区域 "),
      vue.createElementVNode("view", { class: "message-content" }, [
        vue.createCommentVNode(" 系统通知子分类 "),
        $data.currentTab === "system" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "sub-tabs"
        }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["sub-tab-item", { active: $data.currentSubTab === "pickup" }]),
              onClick: _cache[4] || (_cache[4] = ($event) => $options.switchSubTab("pickup"))
            },
            " 取件相关 ",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["sub-tab-item", { active: $data.currentSubTab === "delivery" }]),
              onClick: _cache[5] || (_cache[5] = ($event) => $options.switchSubTab("delivery"))
            },
            " 派件相关 ",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["sub-tab-item", { active: $data.currentSubTab === "sign" }]),
              onClick: _cache[6] || (_cache[6] = ($event) => $options.switchSubTab("sign"))
            },
            " 签收提醒 ",
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["sub-tab-item", { active: $data.currentSubTab === "cancel" }]),
              onClick: _cache[7] || (_cache[7] = ($event) => $options.switchSubTab("cancel"))
            },
            " 快件取消 ",
            2
            /* CLASS */
          )
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 消息列表 "),
        vue.createElementVNode("scroll-view", {
          class: "message-list",
          "scroll-y": "true"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($options.filteredMessages, (message) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "message-item",
                key: message.id,
                onClick: ($event) => $options.readMessage(message)
              }, [
                vue.createElementVNode("view", { class: "message-header" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "message-title" },
                    vue.toDisplayString(message.title),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "message-time" },
                    vue.toDisplayString(message.time),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "message-content" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "message-text" },
                    vue.toDisplayString(message.content),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "message-footer" }, [
                  vue.createElementVNode(
                    "text",
                    {
                      class: vue.normalizeClass(["message-type", message.typeClass])
                    },
                    vue.toDisplayString(message.type),
                    3
                    /* TEXT, CLASS */
                  ),
                  !message.read ? (vue.openBlock(), vue.createElementBlock("text", {
                    key: 0,
                    class: "unread-dot"
                  })) : vue.createCommentVNode("v-if", true)
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createCommentVNode(" 空状态 "),
          $options.filteredMessages.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "empty-state"
          }, [
            vue.createElementVNode("text", { class: "empty-text" }, "暂无消息")
          ])) : vue.createCommentVNode("v-if", true)
        ])
      ])
    ]);
  }
  const PagesMessageIndex = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__scopeId", "data-v-780fc0ad"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/message/index.vue"]]);
  const _sfc_main$5 = {
    data() {
      return {
        statistics: {
          totalTasks: 128,
          completedTasks: 115,
          income: 860,
          pendingPickup: 3,
          pendingDelivery: 5,
          todayCompleted: 28,
          exceptionTasks: 2,
          avgCompletionTime: 45
        },
        chartData: [
          { label: "周一", value: 18, height: "60%" },
          { label: "周二", value: 22, height: "73%" },
          { label: "周三", value: 15, height: "50%" },
          { label: "周四", value: 25, height: "83%" },
          { label: "周五", value: 30, height: "100%" },
          { label: "周六", value: 12, height: "40%" },
          { label: "周日", value: 8, height: "27%" }
        ]
      };
    },
    methods: {
      selectDateRange() {
        uni.showToast({
          title: "日期选择功能开发中",
          icon: "none"
        });
      },
      goToPage(url) {
        uni.switchTab({
          url: `/${url}`,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      }
    }
  };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "statistics-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "header-title" }, "工作统计"),
        vue.createElementVNode("view", { class: "header-actions" }, [
          vue.createElementVNode("text", {
            class: "date-selector",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.selectDateRange && $options.selectDateRange(...args))
          }, "📅")
        ])
      ]),
      vue.createCommentVNode(" 统计概览 "),
      vue.createElementVNode("view", { class: "overview-section" }, [
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.totalTasks),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "总任务数")
        ]),
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.completedTasks),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "完成任务")
        ]),
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.income),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "收入(元)")
        ])
      ]),
      vue.createCommentVNode(" 任务统计图表 "),
      vue.createElementVNode("view", { class: "chart-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "任务完成趋势")
        ]),
        vue.createElementVNode("view", { class: "chart-container" }, [
          vue.createCommentVNode(" 简化的柱状图 "),
          vue.createElementVNode("view", { class: "chart" }, [
            vue.createElementVNode("view", { class: "chart-y-axis" }, [
              vue.createElementVNode("text", { class: "y-label" }, "30"),
              vue.createElementVNode("text", { class: "y-label" }, "20"),
              vue.createElementVNode("text", { class: "y-label" }, "10"),
              vue.createElementVNode("text", { class: "y-label" }, "0")
            ]),
            vue.createElementVNode("view", { class: "chart-content" }, [
              vue.createElementVNode("view", { class: "chart-bars" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.chartData, (item, index) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      class: "bar-item",
                      key: index
                    }, [
                      vue.createElementVNode(
                        "view",
                        {
                          class: "bar",
                          style: vue.normalizeStyle({ height: item.height })
                        },
                        [
                          vue.createElementVNode(
                            "text",
                            { class: "bar-value" },
                            vue.toDisplayString(item.value),
                            1
                            /* TEXT */
                          )
                        ],
                        4
                        /* STYLE */
                      ),
                      vue.createElementVNode(
                        "text",
                        { class: "bar-label" },
                        vue.toDisplayString(item.label),
                        1
                        /* TEXT */
                      )
                    ]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ])
          ])
        ])
      ]),
      vue.createCommentVNode(" 详细统计 "),
      vue.createElementVNode("view", { class: "detail-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "详细统计")
        ]),
        vue.createElementVNode("view", { class: "detail-list" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "待取件"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.pendingPickup),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "待派送"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.pendingDelivery),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "今日完成"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.todayCompleted),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "异常任务"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.exceptionTasks),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "平均完成时间"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.avgCompletionTime) + "分钟",
              1
              /* TEXT */
            )
          ])
        ])
      ])
    ]);
  }
  const PagesStatisticsIndex = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__scopeId", "data-v-6e199430"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/statistics/index.vue"]]);
  const _sfc_main$4 = {
    data() {
      return {
        pickupLocation: "北京市",
        deliveryLocation: "上海市",
        packageWeight: "",
        packageVolume: "",
        selectedPackageType: "document",
        packageTypes: [
          { label: "文件", value: "document" },
          { label: "电子产品", value: "electronics" },
          { label: "衣物", value: "clothing" },
          { label: "食品", value: "food" },
          { label: "易碎品", value: "fragile" },
          { label: "液体", value: "liquid" }
        ],
        freightResult: null
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectPickupLocation() {
        uni.showToast({
          title: "选择寄件地功能开发中",
          icon: "none"
        });
      },
      selectDeliveryLocation() {
        uni.showToast({
          title: "选择收件地功能开发中",
          icon: "none"
        });
      },
      selectPackageType(type) {
        this.selectedPackageType = type;
      },
      calculateFreight() {
        if (!this.pickupLocation || !this.deliveryLocation) {
          uni.showToast({
            title: "请选择寄件地和收件地",
            icon: "none"
          });
          return;
        }
        if (!this.packageWeight && !this.packageVolume) {
          uni.showToast({
            title: "请输入重量或体积",
            icon: "none"
          });
          return;
        }
        const weight = parseFloat(this.packageWeight) || 0;
        const volume = parseFloat(this.packageVolume) || 0;
        const baseFee = 8;
        const additionalWeight = Math.max(0, weight - 1);
        const additionalFee = additionalWeight * 2;
        const volumeWeight = volume / 6e3;
        const volumeFee = volumeWeight > weight ? (volumeWeight - 1) * 2 : 0;
        const specialItemFee = ["fragile", "liquid"].includes(this.selectedPackageType) ? 5 : 0;
        const totalFee = (baseFee + additionalFee + volumeFee + specialItemFee).toFixed(2);
        this.freightResult = {
          baseFee: baseFee.toFixed(2),
          additionalFee: additionalFee.toFixed(2),
          volumeFee: volumeFee.toFixed(2),
          specialItemFee: specialItemFee.toFixed(2),
          totalFee
        };
        uni.showToast({
          title: "计算完成",
          icon: "success"
        });
      }
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "freight-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "运费查询"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 查询表单 "),
      vue.createElementVNode("view", { class: "query-form" }, [
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "寄件地"),
          vue.createElementVNode("view", {
            class: "location-selector",
            onClick: _cache[1] || (_cache[1] = (...args) => $options.selectPickupLocation && $options.selectPickupLocation(...args))
          }, [
            vue.createElementVNode(
              "text",
              { class: "location-text" },
              vue.toDisplayString($data.pickupLocation || "请选择寄件地"),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "arrow" }, "▼")
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "收件地"),
          vue.createElementVNode("view", {
            class: "location-selector",
            onClick: _cache[2] || (_cache[2] = (...args) => $options.selectDeliveryLocation && $options.selectDeliveryLocation(...args))
          }, [
            vue.createElementVNode(
              "text",
              { class: "location-text" },
              vue.toDisplayString($data.deliveryLocation || "请选择收件地"),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "arrow" }, "▼")
          ])
        ]),
        vue.createElementVNode("view", { class: "form-row" }, [
          vue.createElementVNode("view", { class: "form-group half" }, [
            vue.createElementVNode("text", { class: "form-label" }, "重量(kg)"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                type: "digit",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.packageWeight = $event),
                placeholder: "请输入物品重量"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.packageWeight]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-group half" }, [
            vue.createElementVNode("text", { class: "form-label" }, "体积(cm³)"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                type: "digit",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.packageVolume = $event),
                placeholder: "请输入物品体积"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.packageVolume]
            ])
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "物品类型"),
          vue.createElementVNode("view", { class: "type-selector" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.packageTypes, (type) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: vue.normalizeClass(["type-item", { active: $data.selectedPackageType === type.value }]),
                  key: type.value,
                  onClick: ($event) => $options.selectPackageType(type.value)
                }, vue.toDisplayString(type.label), 11, ["onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ]),
        vue.createElementVNode("button", {
          class: "query-btn",
          onClick: _cache[5] || (_cache[5] = (...args) => $options.calculateFreight && $options.calculateFreight(...args))
        }, "查询运费")
      ]),
      vue.createCommentVNode(" 查询结果 "),
      $data.freightResult ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "result-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "费用明细"),
        vue.createElementVNode("view", { class: "fee-item" }, [
          vue.createElementVNode("text", { class: "fee-label" }, "首重费用"),
          vue.createElementVNode(
            "text",
            { class: "fee-value" },
            "¥" + vue.toDisplayString($data.freightResult.baseFee),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "fee-item" }, [
          vue.createElementVNode("text", { class: "fee-label" }, "续重费用"),
          vue.createElementVNode(
            "text",
            { class: "fee-value" },
            "¥" + vue.toDisplayString($data.freightResult.additionalFee),
            1
            /* TEXT */
          )
        ]),
        $data.freightResult.volumeFee > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "fee-item"
        }, [
          vue.createElementVNode("text", { class: "fee-label" }, "体积费用"),
          vue.createElementVNode(
            "text",
            { class: "fee-value" },
            "¥" + vue.toDisplayString($data.freightResult.volumeFee),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true),
        $data.freightResult.specialItemFee > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "fee-item"
        }, [
          vue.createElementVNode("text", { class: "fee-label" }, "特殊物品费"),
          vue.createElementVNode(
            "text",
            { class: "fee-value" },
            "¥" + vue.toDisplayString($data.freightResult.specialItemFee),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true),
        vue.createElementVNode("view", { class: "fee-divider" }),
        vue.createElementVNode("view", { class: "fee-item total" }, [
          vue.createElementVNode("text", { class: "fee-label" }, "总计"),
          vue.createElementVNode(
            "text",
            { class: "fee-value total-value" },
            "¥" + vue.toDisplayString($data.freightResult.totalFee),
            1
            /* TEXT */
          )
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 运费说明 "),
      vue.createElementVNode("view", { class: "freight-info" }, [
        vue.createElementVNode("view", { class: "info-title" }, "运费说明"),
        vue.createElementVNode("view", { class: "info-content" }, [
          vue.createElementVNode("text", { class: "info-text" }, "1. 首重1kg内费用为¥8，续重每1kg费用为¥2"),
          vue.createElementVNode("text", { class: "info-text" }, "2. 体积重量 = 长×宽×高/6000，计费重量取实际重量与体积重量较大者"),
          vue.createElementVNode("text", { class: "info-text" }, "3. 特殊物品(如易碎品、液体等)需额外收取¥5处理费"),
          vue.createElementVNode("text", { class: "info-text" }, "4. 以上费用仅供参考，实际费用以快递员收取为准")
        ])
      ])
    ]);
  }
  const PagesFreightIndex = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__scopeId", "data-v-3f412751"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/freight/index.vue"]]);
  const _sfc_main$3 = {
    data() {
      return {
        selectedService: "pickup",
        // 默认选择取件服务
        serviceTypes: [
          { name: "取件服务", value: "pickup", icon: "📦" },
          { name: "派送服务", value: "delivery", icon: "🚚" },
          { name: "取派一体", value: "both", icon: "🔁" }
        ],
        customerInfo: {
          name: "",
          phone: ""
        },
        pickupInfo: {
          address: ""
        },
        deliveryInfo: {
          address: ""
        },
        appointment: {
          time: ""
        },
        packageInfo: {
          weight: ""
        },
        remark: ""
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectService(service) {
        this.selectedService = service;
      },
      getLocation() {
        uni.showToast({
          title: "获取位置功能开发中",
          icon: "none"
        });
      },
      selectTime() {
        uni.showToast({
          title: "选择时间功能开发中",
          icon: "none"
        });
      },
      submitOrder() {
        if (!this.customerInfo.name) {
          uni.showToast({
            title: "请输入客户姓名",
            icon: "none"
          });
          return;
        }
        if (!this.customerInfo.phone) {
          uni.showToast({
            title: "请输入联系电话",
            icon: "none"
          });
          return;
        }
        if (!this.pickupInfo.address) {
          uni.showToast({
            title: "请输入取件地址",
            icon: "none"
          });
          return;
        }
        if (!this.deliveryInfo.address) {
          uni.showToast({
            title: "请输入派送地址",
            icon: "none"
          });
          return;
        }
        if (!this.appointment.time) {
          uni.showToast({
            title: "请选择预约时间",
            icon: "none"
          });
          return;
        }
        uni.showModal({
          title: "提交订单",
          content: "确认提交专属取寄订单？",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "提交中..."
              });
              setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                  title: "提交成功",
                  icon: "success"
                });
                setTimeout(() => {
                  uni.navigateBack();
                }, 1e3);
              }, 1500);
            }
          }
        });
      }
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "exclusive-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "专属取寄"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 功能介绍 "),
      vue.createElementVNode("view", { class: "intro-section" }, [
        vue.createElementVNode("view", { class: "intro-title" }, "专属取寄服务"),
        vue.createElementVNode("view", { class: "intro-content" }, [
          vue.createElementVNode("text", { class: "intro-text" }, "为您的客户提供专属的快递取寄服务，享受更优质的物流体验")
        ])
      ]),
      vue.createCommentVNode(" 服务类型 "),
      vue.createElementVNode("view", { class: "service-types" }, [
        vue.createElementVNode("view", { class: "section-title" }, "服务类型"),
        vue.createElementVNode("view", { class: "type-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.serviceTypes, (service) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: vue.normalizeClass(["type-item", { active: $data.selectedService === service.value }]),
                key: service.value,
                onClick: ($event) => $options.selectService(service.value)
              }, [
                vue.createElementVNode(
                  "text",
                  { class: "type-icon" },
                  vue.toDisplayString(service.icon),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "type-name" },
                  vue.toDisplayString(service.name),
                  1
                  /* TEXT */
                )
              ], 10, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ]),
      vue.createCommentVNode(" 客户信息 "),
      vue.createElementVNode("view", { class: "customer-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "客户信息"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "客户姓名"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "text",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.customerInfo.name = $event),
              placeholder: "请输入客户姓名"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customerInfo.name]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "联系电话"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "number",
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.customerInfo.phone = $event),
              placeholder: "请输入联系电话"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customerInfo.phone]
          ])
        ])
      ]),
      vue.createCommentVNode(" 取寄信息 "),
      vue.createElementVNode("view", { class: "pickup-delivery-info" }, [
        vue.createElementVNode("view", { class: "section-title" }, "取寄信息"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "取件地址"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.pickupInfo.address = $event),
              placeholder: "请输入取件地址"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.pickupInfo.address]
          ]),
          vue.createElementVNode("button", {
            class: "location-btn",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.getLocation && $options.getLocation(...args))
          }, "获取当前位置")
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "派送地址"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => $data.deliveryInfo.address = $event),
              placeholder: "请输入派送地址"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.deliveryInfo.address]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-row" }, [
          vue.createElementVNode("view", { class: "form-group half" }, [
            vue.createElementVNode("text", { class: "form-label" }, "预约时间"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                type: "text",
                "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => $data.appointment.time = $event),
                placeholder: "请选择预约时间",
                onClick: _cache[7] || (_cache[7] = (...args) => $options.selectTime && $options.selectTime(...args))
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.appointment.time]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-group half" }, [
            vue.createElementVNode("text", { class: "form-label" }, "物品重量(kg)"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                type: "digit",
                "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.packageInfo.weight = $event),
                placeholder: "请输入物品重量"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.packageInfo.weight]
            ])
          ])
        ])
      ]),
      vue.createCommentVNode(" 备注信息 "),
      vue.createElementVNode("view", { class: "remark-section" }, [
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[9] || (_cache[9] = ($event) => $data.remark = $event),
              placeholder: "请输入备注信息，如特殊要求等"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.remark]
          ])
        ])
      ]),
      vue.createCommentVNode(" 提交按钮 "),
      vue.createElementVNode("view", { class: "submit-section" }, [
        vue.createElementVNode("button", {
          class: "submit-btn",
          onClick: _cache[10] || (_cache[10] = (...args) => $options.submitOrder && $options.submitOrder(...args))
        }, "提交订单")
      ])
    ]);
  }
  const PagesExclusiveIndex = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__scopeId", "data-v-922ee060"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/exclusive/index.vue"]]);
  const _sfc_main$2 = {
    data() {
      return {
        selectedDate: "2023-04-01",
        // 默认选中日期
        pickupTasks: [
          {
            id: "T20230401001",
            type: "pickup",
            status: "pending",
            statusText: "待取件",
            statusClass: "status-pending",
            pickupAddress: "北京市海淀区某某大厦202室",
            pickupTime: "2023-04-01 10:00-12:00",
            senderName: "李女士",
            senderPhone: "139****9999",
            distance: "2.5",
            actionText: "去取件",
            actionClass: "action-pickup"
          },
          {
            id: "T20230401002",
            type: "pickup",
            status: "completed",
            statusText: "已取件",
            statusClass: "status-completed",
            pickupAddress: "北京市朝阳区某某小区3号楼501室",
            pickupTime: "2023-04-01 14:00-16:00",
            senderName: "王先生",
            senderPhone: "138****8888",
            distance: "5.2",
            actionText: "已完成",
            actionClass: "action-completed"
          }
        ],
        deliveryTasks: [
          {
            id: "T20230401004",
            type: "delivery",
            status: "pending",
            statusText: "待派送",
            statusClass: "status-pending",
            deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
            deliveryTime: "2023-04-01 15:00-17:00",
            customerName: "张先生",
            customerPhone: "138****8888",
            distance: "3.2",
            actionText: "去派送",
            actionClass: "action-delivery"
          },
          {
            id: "T20230401005",
            type: "delivery",
            status: "completed",
            statusText: "已签收",
            statusClass: "status-completed",
            deliveryAddress: "北京市海淀区某某科技园A座508室",
            deliveryTime: "2023-04-01 16:00-18:00",
            customerName: "刘女士",
            customerPhone: "139****9999",
            distance: "6.7",
            actionText: "已完成",
            actionClass: "action-completed"
          }
        ]
      };
    },
    computed: {
      allTasks() {
        return [...this.pickupTasks, ...this.deliveryTasks];
      },
      pickupCount() {
        return this.pickupTasks.length;
      },
      deliveryCount() {
        return this.deliveryTasks.length;
      }
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectDate() {
        uni.showToast({
          title: "日期选择功能开发中",
          icon: "none"
        });
      },
      goToTaskDetail(taskId) {
        uni.navigateTo({
          url: `/pages/task/detail?id=${taskId}`
        });
      },
      handleTaskAction(task) {
        if (task.type === "pickup" && task.status === "pending") {
          uni.navigateTo({
            url: `/pages/task/pickup.vue?id=${task.id}`
          });
        } else if (task.type === "delivery" && task.status === "pending") {
          uni.navigateTo({
            url: `/pages/task/delivery.vue?id=${task.id}`
          });
        }
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "all-tasks-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "全部取派"),
        vue.createElementVNode("view", { class: "header-right" }, [
          vue.createElementVNode("text", {
            class: "date-selector",
            onClick: _cache[1] || (_cache[1] = (...args) => $options.selectDate && $options.selectDate(...args))
          }, "📅")
        ])
      ]),
      vue.createCommentVNode(" 日期筛选 "),
      vue.createElementVNode("view", { class: "date-filter" }, [
        vue.createElementVNode("view", { class: "date-display" }, [
          vue.createElementVNode(
            "text",
            { class: "date-text" },
            vue.toDisplayString($data.selectedDate),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 任务统计 "),
      vue.createElementVNode("view", { class: "task-stats" }, [
        vue.createElementVNode("view", { class: "stat-item" }, [
          vue.createElementVNode(
            "text",
            { class: "stat-value" },
            vue.toDisplayString($options.pickupCount),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "stat-label" }, "取件任务")
        ]),
        vue.createElementVNode("view", { class: "stat-item" }, [
          vue.createElementVNode(
            "text",
            { class: "stat-value" },
            vue.toDisplayString($options.deliveryCount),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "stat-label" }, "派件任务")
        ])
      ]),
      vue.createCommentVNode(" 日期筛选 "),
      vue.createElementVNode("view", { class: "date-filter" }, [
        vue.createElementVNode("view", { class: "date-display" }, [
          vue.createElementVNode(
            "text",
            { class: "date-text" },
            vue.toDisplayString($data.selectedDate),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 任务统计 "),
      vue.createElementVNode("view", { class: "task-stats" }, [
        vue.createElementVNode("view", { class: "stat-item" }, [
          vue.createElementVNode(
            "text",
            { class: "stat-value" },
            vue.toDisplayString($options.pickupCount),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "stat-label" }, "取件任务")
        ]),
        vue.createElementVNode("view", { class: "stat-item" }, [
          vue.createElementVNode(
            "text",
            { class: "stat-value" },
            vue.toDisplayString($options.deliveryCount),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "stat-label" }, "派件任务")
        ])
      ]),
      vue.createCommentVNode(" 任务列表 "),
      vue.createElementVNode("scroll-view", {
        class: "task-list",
        "scroll-y": "true"
      }, [
        (vue.openBlock(true), vue.createElementBlock(
          vue.Fragment,
          null,
          vue.renderList($options.allTasks, (task) => {
            return vue.openBlock(), vue.createElementBlock("view", {
              class: "task-item",
              key: task.id,
              onClick: ($event) => $options.goToTaskDetail(task.id)
            }, [
              vue.createElementVNode("view", { class: "task-header" }, [
                vue.createElementVNode(
                  "text",
                  { class: "task-id" },
                  "任务单号: " + vue.toDisplayString(task.id),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  {
                    class: vue.normalizeClass(["task-status", task.statusClass])
                  },
                  vue.toDisplayString(task.statusText),
                  3
                  /* TEXT, CLASS */
                )
              ]),
              vue.createElementVNode("view", { class: "task-content" }, [
                task.type === "pickup" ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "task-address"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "寄件地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.pickupAddress),
                    1
                    /* TEXT */
                  )
                ])) : (vue.openBlock(), vue.createElementBlock("view", {
                  key: 1,
                  class: "task-address"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "收货地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.deliveryAddress),
                    1
                    /* TEXT */
                  )
                ])),
                vue.createElementVNode("view", { class: "task-time" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "label" },
                    vue.toDisplayString(task.type === "pickup" ? "预约时间:" : "派送时间:"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "time" },
                    vue.toDisplayString(task.type === "pickup" ? task.pickupTime : task.deliveryTime),
                    1
                    /* TEXT */
                  )
                ]),
                task.type === "pickup" ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 2,
                  class: "task-customer"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "寄件人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.senderName) + " " + vue.toDisplayString(task.senderPhone),
                    1
                    /* TEXT */
                  )
                ])) : (vue.openBlock(), vue.createElementBlock("view", {
                  key: 3,
                  class: "task-customer"
                }, [
                  vue.createElementVNode("text", { class: "label" }, "收货人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.customerName) + " " + vue.toDisplayString(task.customerPhone),
                    1
                    /* TEXT */
                  )
                ]))
              ]),
              vue.createElementVNode("view", { class: "task-footer" }, [
                vue.createElementVNode(
                  "text",
                  { class: "distance" },
                  vue.toDisplayString(task.distance) + "km",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode("button", {
                  class: vue.normalizeClass(["action-btn", task.actionClass]),
                  onClick: vue.withModifiers(($event) => $options.handleTaskAction(task), ["stop"])
                }, vue.toDisplayString(task.actionText), 11, ["onClick"])
              ])
            ], 8, ["onClick"]);
          }),
          128
          /* KEYED_FRAGMENT */
        )),
        vue.createCommentVNode(" 空状态 "),
        $options.allTasks.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无任务")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesTaskAll = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__scopeId", "data-v-4efbdb44"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/all.vue"]]);
  const _sfc_main$1 = {
    setup() {
      const paymentAmount = vue.ref("0.00");
      const paymentMethod = vue.ref("微信支付");
      const transactionId = vue.ref("");
      const paymentTime = vue.ref("");
      const orderId = vue.ref("");
      const generateTransactionId = () => {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1e4);
        return `TXN${timestamp}${random}`;
      };
      const formatTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };
      const goBack = () => {
        uni.navigateBack();
      };
      const goToTaskList = () => {
        uni.switchTab({
          url: "/pages/task/list",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      const goToHome = () => {
        uni.switchTab({
          url: "/pages/index/index",
          animationType: "slide-in-right",
          animationDuration: 300
        });
      };
      vue.onMounted(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const options = currentPage.options || {};
        paymentAmount.value = options.amount || "15.50";
        paymentMethod.value = options.method || "微信支付";
        orderId.value = options.orderId || "SF1234567890";
        transactionId.value = generateTransactionId();
        paymentTime.value = formatTime(/* @__PURE__ */ new Date());
      });
      return {
        paymentAmount,
        paymentMethod,
        transactionId,
        paymentTime,
        orderId,
        goBack,
        goToTaskList,
        goToHome
      };
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "payment-success-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $setup.goBack && $setup.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "支付成功"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 成功状态 "),
      vue.createElementVNode("view", { class: "success-section" }, [
        vue.createElementVNode("view", { class: "success-icon-container" }, [
          vue.createElementVNode("text", { class: "success-icon" }, "✅"),
          vue.createElementVNode("view", { class: "success-animation" })
        ]),
        vue.createElementVNode("text", { class: "success-title" }, "支付成功"),
        vue.createElementVNode("text", { class: "success-desc" }, "您的付款已成功处理")
      ]),
      vue.createCommentVNode(" 支付信息 "),
      vue.createElementVNode("view", { class: "payment-info" }, [
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "支付金额"),
          vue.createElementVNode(
            "text",
            { class: "value amount" },
            "¥" + vue.toDisplayString($setup.paymentAmount),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "支付方式"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($setup.paymentMethod),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "交易单号"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($setup.transactionId),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "支付时间"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($setup.paymentTime),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "info-item" }, [
          vue.createElementVNode("text", { class: "label" }, "订单号"),
          vue.createElementVNode(
            "text",
            { class: "value" },
            vue.toDisplayString($setup.orderId),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-section" }, [
        vue.createElementVNode("button", {
          class: "action-btn primary-btn",
          onClick: _cache[1] || (_cache[1] = (...args) => $setup.goToTaskList && $setup.goToTaskList(...args))
        }, "返回任务列表"),
        vue.createElementVNode("button", {
          class: "action-btn secondary-btn",
          onClick: _cache[2] || (_cache[2] = (...args) => $setup.goToHome && $setup.goToHome(...args))
        }, "返回首页")
      ]),
      vue.createCommentVNode(" 温馨提示 "),
      vue.createElementVNode("view", { class: "tips-section" }, [
        vue.createElementVNode("text", { class: "tips-title" }, "温馨提示"),
        vue.createElementVNode("text", { class: "tips-text" }, "• 支付凭证已保存，可在历史记录中查看"),
        vue.createElementVNode("text", { class: "tips-text" }, "• 如有疑问，请联系客服：400-123-4567"),
        vue.createElementVNode("text", { class: "tips-text" }, "• 感谢您使用神领物流服务")
      ])
    ]);
  }
  const PagesPaymentSuccess = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-cbafefc3"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/payment/success.vue"]]);
  __definePage("pages/index/launch", PagesIndexLaunch);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/task/list", PagesTaskList);
  __definePage("pages/task/delivery-list", PagesTaskDeliveryList);
  __definePage("pages/profile/index", PagesProfileIndex);
  __definePage("pages/task/detail", PagesTaskDetail);
  __definePage("pages/task/delivery", PagesTaskDelivery);
  __definePage("pages/search/index", PagesSearchIndex);
  __definePage("pages/message/index", PagesMessageIndex);
  __definePage("pages/statistics/index", PagesStatisticsIndex);
  __definePage("pages/freight/index", PagesFreightIndex);
  __definePage("pages/exclusive/index", PagesExclusiveIndex);
  __definePage("pages/task/all", PagesTaskAll);
  __definePage("pages/payment/success", PagesPaymentSuccess);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    },
    globalData: {
      pageTransition: "slide"
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
