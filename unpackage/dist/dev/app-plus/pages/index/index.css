
/* 首页容器 */
.home-container[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding: 0 0 calc(3.75rem + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 状态栏占位 - 不再需要 */
.status-bar[data-v-1cf27b2a] {
  display: none;
}

/* 顶部搜索栏 */
.search-bar[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
  position: fixed;  /* 固定定位 */
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}
.search-icon[data-v-1cf27b2a] {
  font-size: 0.875rem;
  margin-right: 0.46875rem;
  color: rgba(255, 255, 255, 0.8);
}
.search-placeholder[data-v-1cf27b2a] {
  flex: 1;
  font-size: 0.875rem;
  opacity: 0.8;
  color: rgba(255, 255, 255, 0.8);
}
.search-actions[data-v-1cf27b2a] {
  position: relative;
  display: flex;
  align-items: center;
}
.message-icon[data-v-1cf27b2a] {
  font-size: 1.125rem;
  padding: 0.3125rem;
}
.unread-badge[data-v-1cf27b2a] {
  position: absolute;
  top: -0.15625rem;
  right: -0.15625rem;
  background-color: #e74c3c;
  color: white;
  font-size: 0.625rem;
  width: 0.9375rem;
  height: 0.9375rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 核心任务概览 */
.task-overview[data-v-1cf27b2a] {
  display: flex;
  padding: 0.9375rem;
  background-color: var(--bg-white);
  margin: 4.375rem 0.625rem 0.625rem 0.625rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.task-card[data-v-1cf27b2a] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0.5rem;
  min-height: 3.75rem;
}
.task-card[data-v-1cf27b2a]:not(:last-child) {
  border-right: 0.03125rem solid var(--border-color);
}
.card-title[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: var(--text-gray);
  margin-bottom: 0.375rem;
  text-align: center;
  line-height: 1.2;
}
.card-value[data-v-1cf27b2a] {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1;
}

/* 消息滚动栏 */
.message-scroll[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  padding: 0.46875rem 0.9375rem;
  background-color: var(--bg-white);
  margin-bottom: 0.625rem;
}
.message-icon[data-v-1cf27b2a] {
  font-size: 0.875rem;
  margin-right: 0.46875rem;
  color: var(--primary-color);
}
.message-swiper[data-v-1cf27b2a] {
  flex: 1;
  height: 1.25rem;
}
.message-text[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: var(--text-gray);
}

/* 常用功能区 */
.quick-actions[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-around;
  background-color: var(--bg-white);
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  border-radius: 0.3125rem;
}
.action-item[data-v-1cf27b2a] {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.action-icon[data-v-1cf27b2a] {
  font-size: 1.5rem;
  margin-bottom: 0.3125rem;
}
.action-label[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: var(--text-dark);
}

/* 今日数据统计 */
.today-stats[data-v-1cf27b2a] {
  display: flex;
  padding: 0.9375rem;
  background-color: var(--bg-white);
  margin: 0.625rem 0;
}
.stat-item[data-v-1cf27b2a] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-label[data-v-1cf27b2a] {
  font-size: 0.875rem;
  color: var(--text-gray);
  margin-bottom: 0.3125rem;
}
.stat-value[data-v-1cf27b2a] {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--primary-color);
}

/* 待办任务列表 */
.pending-tasks[data-v-1cf27b2a] {
  flex: 1;
  background-color: var(--bg-white);
  margin: 0.625rem 0;
  padding: 0 0.9375rem 0.9375rem;
}
.section-header[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid var(--border-color);
}
.section-title[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
}
.view-all[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: var(--primary-color);
}
.task-list[data-v-1cf27b2a] {
  padding: 0.625rem 0;
}

/* 任务卡片样式 */
.task-card[data-v-1cf27b2a] {
  background-color: var(--bg-white);
  border-radius: 0.5rem;
  margin-bottom: 0.625rem;
  padding: 0.75rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
  border: 0.03125rem solid var(--border-color);
  position: relative;
  overflow: hidden;
}
.task-card[data-v-1cf27b2a]:last-child {
  margin-bottom: 0;
}

/* 任务类型标识 */
.task-type-badge[data-v-1cf27b2a] {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0 0.5rem 0 0.5rem;
  font-size: 0.75rem;
  z-index: 2;
}
.task-type-badge.pickup[data-v-1cf27b2a] {
  background-color: #e8f5e8;
  color: #2e7d32;
}
.task-type-badge.delivery[data-v-1cf27b2a] {
  background-color: #e3f2fd;
  color: #1976d2;
}
.type-icon[data-v-1cf27b2a] {
  margin-right: 0.1875rem;
  font-size: 0.875rem;
}
.type-text[data-v-1cf27b2a] {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 主要信息区域 */
.task-main-info[data-v-1cf27b2a] {
  margin-top: 0.625rem;
  margin-bottom: 0.75rem;
}

/* 客户信息 */
.customer-section[data-v-1cf27b2a] {
  margin-bottom: 0.5rem;
  padding-right: 2.5rem; /* 为右上角标识留出空间 */
}
.customer-header[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
  flex-wrap: wrap;
}
.customer-name[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  flex: 1;
  margin-right: 0.375rem;
}
.distance-badge[data-v-1cf27b2a] {
  background-color: var(--accent-color);
  color: var(--text-white);
  padding: 0.1875rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}
.distance-text[data-v-1cf27b2a] {
  font-size: 0.6875rem;
  font-weight: 500;
}
.customer-phone[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: var(--text-gray);
}

/* 地址信息 */
.address-section[data-v-1cf27b2a] {
  margin-bottom: 0.5rem;
  padding: 0.375rem 0.5rem;
  background-color: var(--bg-light);
  border-radius: 0.25rem;
  border-left: 0.125rem solid var(--primary-color);
  min-height: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.address-label[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: var(--text-gray);
  display: block;
  margin-bottom: 0.1875rem;
}
.address-text[data-v-1cf27b2a] {
  font-size: 0.875rem;
  color: var(--text-dark);
  line-height: 1.4;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 时间信息 */
.time-section[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.time-label[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: var(--text-gray);
  margin-right: 0.375rem;
}
.time-text[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section[data-v-1cf27b2a] {
  border-top: 0.03125rem solid var(--border-color);
  padding-top: 0.5rem;
}
.action-buttons[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}
.action-btn[data-v-1cf27b2a] {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.375rem;
  border-radius: 0.25rem;
  border: none;
  font-size: 0.75rem;
  min-height: 1.875rem;
  min-width: 3.75rem;
}
.call-btn[data-v-1cf27b2a] {
  background-color: #e8f5e8;
  color: #2e7d32;
}
.message-btn[data-v-1cf27b2a] {
  background-color: #fff3e0;
  color: #f57c00;
}
.cancel-btn[data-v-1cf27b2a] {
  background-color: #ffebee;
  color: #d32f2f;
}
.deliver-btn[data-v-1cf27b2a] {
  background-color: #e3f2fd;
  color: #1976d2;
}
.btn-icon[data-v-1cf27b2a] {
  font-size: 0.875rem;
  margin-right: 0.1875rem;
}
.btn-text[data-v-1cf27b2a] {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 底部导航栏 */
.bottom-tab-bar[data-v-1cf27b2a] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: var(--bg-white);
  border-top: 0.03125rem solid var(--border-color);
  display: flex;
  padding: 0.3125rem 0 env(safe-area-inset-bottom);
  z-index: 999;
}
.tab-item[data-v-1cf27b2a] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  color: var(--text-gray);
}
.tab-item.active[data-v-1cf27b2a] {
  color: var(--primary-color);
}
.tab-icon[data-v-1cf27b2a] {
  font-size: 1.125rem;
  margin-bottom: 0.1875rem;
}
.scan-center[data-v-1cf27b2a] {
  flex: 1.5;
  background-color: var(--primary-color);
  border-radius: 1.5625rem;
  margin: 0 0.625rem;
  color: var(--text-white);
}
