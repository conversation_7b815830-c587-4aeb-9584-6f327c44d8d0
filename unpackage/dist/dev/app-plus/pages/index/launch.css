
.launch-container[data-v-8d8376b8] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #3498db, #8e44ad);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}
.logo-container[data-v-8d8376b8] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.app-logo[data-v-8d8376b8] {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 1.5625rem;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.8s ease-out;
}
.app-logo.logo-animation[data-v-8d8376b8] {
  opacity: 1;
  transform: scale(1);
}
.app-name[data-v-8d8376b8] {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 3.125rem;
  opacity: 0;
  transform: translateY(0.9375rem);
  transition: all 0.8s ease-out 0.3s;
}
.app-name.name-animation[data-v-8d8376b8] {
  opacity: 1;
  transform: translateY(0);
}
.loading-dots[data-v-8d8376b8] {
  display: flex;
  flex-direction: row;
  z-index: 9999;
  position: relative;
}
.dot[data-v-8d8376b8] {
  font-size: 1.875rem;
  color: #ffffff;
  opacity: 0;
  animation: none;
  margin: 0 0.15625rem;
}
.dot.dot-animation[data-v-8d8376b8] {
  animation: dotBlink-8d8376b8 1.5s infinite;
}
@keyframes dotBlink-8d8376b8 {
0%, 100% {
    opacity: 0;
}
50% {
    opacity: 1;
}
}
