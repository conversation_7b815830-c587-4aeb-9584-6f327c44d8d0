
/* 容器 */
.message-container[data-v-780fc0ad] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
  padding-top: env(safe-area-inset-top);
}

/* 状态栏占位 */
.status-bar[data-v-780fc0ad] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: var(--primary-color);
}

/* 页面头部 */
.header[data-v-780fc0ad] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}
.header-left[data-v-780fc0ad] {
  display: flex;
  align-items: center;
  padding: 0.3125rem;
}
.back-icon[data-v-780fc0ad] {
  font-size: 1.125rem;
  margin-right: 0.3125rem;
  color: var(--text-white);
}
.back-text[data-v-780fc0ad] {
  font-size: 0.875rem;
  color: var(--text-white);
}
.header-title[data-v-780fc0ad] {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.125rem;
  font-weight: bold;
}
.header-title[data-v-780fc0ad] {
  font-size: 1.125rem;
  font-weight: bold;
}
.mark-all-read[data-v-780fc0ad] {
  font-size: 0.875rem;
}

/* 消息分类标签 */
.message-tabs[data-v-780fc0ad] {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.tab-item[data-v-780fc0ad] {
  flex: 1;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.875rem;
  color: var(--text-gray);
}
.tab-item.active[data-v-780fc0ad] {
  color: var(--primary-color);
  border-bottom: 0.125rem solid var(--primary-color);
}

/* 系统通知子分类 */
.sub-tabs[data-v-780fc0ad] {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
  padding: 0 0.625rem;
}
.sub-tab-item[data-v-780fc0ad] {
  flex: 1;
  text-align: center;
  padding: 0.46875rem 0;
  font-size: 0.75rem;
  color: var(--text-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sub-tab-item.active[data-v-780fc0ad] {
  color: var(--primary-color);
  border-bottom: 0.125rem solid var(--primary-color);
}

/* 消息列表 */
.message-list[data-v-780fc0ad] {
  flex: 1;
}
.message-item[data-v-780fc0ad] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.625rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.message-header[data-v-780fc0ad] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.message-title[data-v-780fc0ad] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: var(--text-dark);
}
.message-time[data-v-780fc0ad] {
  font-size: 0.75rem;
  color: var(--text-gray);
}
.message-content[data-v-780fc0ad] {
  margin-bottom: 0.3125rem;
}
.message-text[data-v-780fc0ad] {
  font-size: 0.8125rem;
  color: var(--text-gray);
  line-height: 1.5;
}
.message-footer[data-v-780fc0ad] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.message-type[data-v-780fc0ad] {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.1875rem;
  color: var(--text-white);
}
.message-type.pickup[data-v-780fc0ad] {
  background-color: var(--accent-color);
}
.message-type.delivery[data-v-780fc0ad] {
  background-color: #3498db;
}
.message-type.sign[data-v-780fc0ad] {
  background-color: #27ae60;
}
.message-type.cancel[data-v-780fc0ad] {
  background-color: #95a5a6;
}
.message-type.notice[data-v-780fc0ad] {
  background-color: #f39c12;
}
.unread-dot[data-v-780fc0ad] {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* 空状态 */
.empty-state[data-v-780fc0ad] {
  text-align: center;
  padding: 3.125rem 0;
}
.empty-text[data-v-780fc0ad] {
  font-size: 0.875rem;
  color: var(--text-gray);
}
