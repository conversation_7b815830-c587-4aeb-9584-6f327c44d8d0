
.theme-switcher[data-v-d495b30c] {
  display: flex;
  justify-content: center;
  margin-top: 0.625rem;
}
.theme-btn[data-v-d495b30c] {
  padding: 0.3125rem 0.625rem;
  margin: 0 0.3125rem;
  border-radius: 0.25rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}
.theme-btn.active[data-v-d495b30c] {
  background-color: #4caf50;
  color: #fff;
}


/* 容器 */
.task-detail-container[data-v-43b93a3d] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
  padding-top: env(safe-area-inset-top);
}

/* 状态栏占位 */
.status-bar[data-v-43b93a3d] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
}

/* 页面头部 */
.header[data-v-43b93a3d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
}
.header-left[data-v-43b93a3d] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-43b93a3d] {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-43b93a3d] {
  font-size: 0.875rem;
}
.header-title[data-v-43b93a3d] {
  font-size: 1.125rem;
  font-weight: bold;
}
.more-icon[data-v-43b93a3d] {
  font-size: 1.125rem;
}

/* 任务状态 */
.task-status[data-v-43b93a3d] {
  background-color: white;
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}
.status-badge[data-v-43b93a3d] {
  padding: 0.3125rem 0.625rem;
  border-radius: 0.1875rem;
  font-size: 0.8125rem;
  color: white;
  margin-right: 0.9375rem;
}
.status-badge.pending[data-v-43b93a3d] {
  background-color: #f39c12;
}
.status-info[data-v-43b93a3d] {
  display: flex;
  flex-direction: column;
}
.task-id[data-v-43b93a3d],
.task-time[data-v-43b93a3d] {
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.3125rem;
}
.task-time[data-v-43b93a3d]:last-child {
  margin-bottom: 0;
}

/* 内容区域 */
.customer-info[data-v-43b93a3d],
.package-info[data-v-43b93a3d],
.fee-info[data-v-43b93a3d],
.operation-records[data-v-43b93a3d] {
  background-color: white;
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.05);
}
.section-title[data-v-43b93a3d] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
}
.info-item[data-v-43b93a3d],
.fee-item[data-v-43b93a3d],
.record-item[data-v-43b93a3d] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
}
.info-item[data-v-43b93a3d]:last-child,
.fee-item[data-v-43b93a3d]:last-child,
.record-item[data-v-43b93a3d]:last-child {
  margin-bottom: 0;
}
.label[data-v-43b93a3d],
.record-time[data-v-43b93a3d] {
  font-size: 0.8125rem;
  color: #666;
}
.value[data-v-43b93a3d],
.record-desc[data-v-43b93a3d] {
  font-size: 0.8125rem;
  color: #333;
  text-align: right;
}
.fee-item.total[data-v-43b93a3d] {
  border-top: 0.03125rem solid #eee;
  padding-top: 0.625rem;
  margin-top: 0.625rem;
  font-weight: bold;
}
.total-value[data-v-43b93a3d] {
  color: #e74c3c;
  font-size: 1rem;
}

/* 底部操作 */
.bottom-actions[data-v-43b93a3d] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 0.625rem 0.9375rem;
  padding-bottom: calc(0.625rem + env(safe-area-inset-bottom));
  border-top: 0.03125rem solid #eee;
  display: flex;
  gap: 0.625rem;
}
.action-btn[data-v-43b93a3d] {
  flex: 1;
  height: 2.5rem;
  font-size: 0.9375rem;
  border-radius: 0.3125rem;
  border: none;
}
.call-btn[data-v-43b93a3d] {
  background-color: #f5f5f5;
  color: #333;
}
.primary-btn[data-v-43b93a3d] {
  background-color: #3498db;
  color: white;
}
