
/* 容器 */
.delivery-container[data-v-f4f36846] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header[data-v-f4f36846] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: var(--primary-color);
  color: var(--text-white);
}
.header-left[data-v-f4f36846] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-f4f36846] {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-f4f36846] {
  font-size: 0.875rem;
}
.header-title[data-v-f4f36846] {
  font-size: 1.125rem;
  font-weight: bold;
}
.more-icon[data-v-f4f36846] {
  font-size: 1.125rem;
}

/* 内容区域 */
.order-info[data-v-f4f36846],
.recipient-info[data-v-f4f36846],
.package-info[data-v-f4f36846],
.signatory-selection[data-v-f4f36846] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.section-title[data-v-f4f36846] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.info-item[data-v-f4f36846] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
}
.info-item[data-v-f4f36846]:last-child {
  margin-bottom: 0;
}
.label[data-v-f4f36846] {
  font-size: 0.8125rem;
  color: var(--text-gray);
}
.value[data-v-f4f36846] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  text-align: right;
  flex: 1;
  margin-left: 0.625rem;
}

/* 签收人选择 */
.signatory-options[data-v-f4f36846] {
  display: flex;
  gap: 0.625rem;
  margin-bottom: 0.9375rem;
}
.option-item[data-v-f4f36846] {
  padding: 0.46875rem 0.78125rem;
  font-size: 0.8125rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
  color: var(--text-dark);
}
.option-item.active[data-v-f4f36846] {
  background-color: var(--primary-color);
  color: var(--text-white);
}
.signatory-input[data-v-f4f36846] {
  width: 100%;
  height: 2.1875rem;
  padding: 0 0.625rem;
  font-size: 0.8125rem;
  border: 0.03125rem solid var(--border-color);
  border-radius: 0.3125rem;
  box-sizing: border-box;
}

/* 核心操作 */
.action-section[data-v-f4f36846] {
  padding: 0.625rem 0.9375rem;
}
.action-btn[data-v-f4f36846] {
  width: 100%;
  height: 2.5rem;
  font-size: 0.9375rem;
  border-radius: 0.3125rem;
  border: none;
  margin-bottom: 0.625rem;
}
.action-btn[data-v-f4f36846]:last-child {
  margin-bottom: 0;
}
.confirm-btn[data-v-f4f36846] {
  background-color: var(--primary-color);
  color: var(--text-white);
}
.exception-btn[data-v-f4f36846] {
  background-color: #e74c3c;
  color: var(--text-white);
}
