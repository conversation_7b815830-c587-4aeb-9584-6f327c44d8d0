
/* 任务容器 */
.task-container[data-v-f4134a5b] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
  position: relative;
}

/* 悬浮头部区域 */
.fixed-header[data-v-f4134a5b] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--bg-white);
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.1);
}

/* 页面头部 */
.header[data-v-f4134a5b] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}
.header-title[data-v-f4134a5b] {
  font-size: 1.125rem;
  font-weight: bold;
}
.refresh-btn[data-v-f4134a5b] {
  font-size: 0.875rem;
}

/* 任务列表 */
.task-list[data-v-f4134a5b] {
  flex: 1;
  height: 0; /* 配合flex: 1 使用 */
  margin-top: 6.25rem;  /* 为固定头部留出空间 */
  padding-top: 0.625rem;
}

/* 状态标签 */
.status-tabs[data-v-f4134a5b] {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.tab-item[data-v-f4134a5b] {
  flex: 1;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.875rem;
  color: var(--text-gray);
}
.tab-item.active[data-v-f4134a5b] {
  color: var(--primary-color);
  border-bottom: 0.125rem solid var(--primary-color);
}

/* 筛选与排序 */
.filter-sort-container[data-v-f4134a5b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.filter-options[data-v-f4134a5b] {
  flex: 1;
}
.filter-btn[data-v-f4134a5b] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3125rem 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
  font-size: 0.8125rem;
}
.arrow[data-v-f4134a5b] {
  margin-left: 0.3125rem;
  font-size: 0.625rem;
}
.search-box[data-v-f4134a5b] {
  display: flex;
  align-items: center;
  margin-left: 0.625rem;
  padding: 0.375rem 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  border: 0.03125rem solid transparent;
}
.search-box[data-v-f4134a5b]:active {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}
.search-icon[data-v-f4134a5b] {
  margin-right: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-gray);
}
.search-placeholder[data-v-f4134a5b] {
  font-size: 0.875rem;
  color: var(--text-gray);
}

/* 任务列表 */
.task-list[data-v-f4134a5b] {
  flex: 1;
}
.task-item[data-v-f4134a5b] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
  padding: 0.625rem;
}
.task-header[data-v-f4134a5b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.task-id[data-v-f4134a5b] {
  font-size: 0.8125rem;
  color: var(--text-gray);
}
.task-status[data-v-f4134a5b] {
  padding: 0.125rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
  font-weight: bold;
}
.task-status.status-pending[data-v-f4134a5b] {
  background-color: var(--warning-color);
  color: var(--text-white);
}
.task-status.status-completed[data-v-f4134a5b] {
  background-color: var(--success-color);
  color: var(--text-white);
}
.task-content[data-v-f4134a5b] {
  margin-bottom: 0.625rem;
}
.task-address[data-v-f4134a5b] {
  display: flex;
  margin-bottom: 0.46875rem;
}
.task-address .label[data-v-f4134a5b] {
  font-size: 0.8125rem;
  color: var(--text-gray);
  margin-right: 0.3125rem;
  flex-shrink: 0;
}
.task-address .address[data-v-f4134a5b] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  line-height: 1.4;
  flex: 1;
}
.task-time[data-v-f4134a5b],
.task-customer[data-v-f4134a5b] {
  display: flex;
  margin-bottom: 0.3125rem;
}
.task-time .label[data-v-f4134a5b],
.task-customer .label[data-v-f4134a5b] {
  font-size: 0.8125rem;
  color: var(--text-gray);
  margin-right: 0.3125rem;
  flex-shrink: 0;
}
.task-time .time[data-v-f4134a5b],
.task-customer .customer[data-v-f4134a5b] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  flex: 1;
}
.task-footer[data-v-f4134a5b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.task-footer[data-v-f4134a5b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.distance[data-v-f4134a5b] {
  font-size: 0.75rem;
  color: var(--text-gray);
}
.action-btn[data-v-f4134a5b] {
  padding: 0.3125rem 0.625rem;
  border-radius: 0.25rem;
  font-size: 0.8125rem;
  border: none;
}
.action-btn.action-delivery[data-v-f4134a5b] {
  background-color: var(--primary-color);
  color: var(--text-white);
}
.action-btn.action-completed[data-v-f4134a5b] {
  background-color: var(--success-color);
  color: var(--text-white);
}

/* 批量管理 */
.batch-management[data-v-f4134a5b] {
  position: fixed;
  bottom: calc(3.75rem + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  padding: 0.625rem 0.9375rem;
  border-top: 0.03125rem solid var(--border-color);
}
.batch-actions[data-v-f4134a5b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.selectAll[data-v-f4134a5b] {
  display: flex;
  align-items: center;
}
.selectAll uni-text[data-v-f4134a5b] {
  margin-left: 0.3125rem;
  font-size: 0.875rem;
}
.action-buttons[data-v-f4134a5b] {
  display: flex;
  gap: 0.625rem;
}
.batch-btn[data-v-f4134a5b] {
  padding: 0.3125rem 0.625rem;
  border-radius: 0.25rem;
  font-size: 0.8125rem;
  border: 0.03125rem solid var(--border-color);
}
.batch-btn.delete[data-v-f4134a5b] {
  background-color: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

/* 空状态 */
.empty-state[data-v-f4134a5b] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-icon[data-v-f4134a5b] {
  font-size: 3.75rem;
  margin-bottom: 0.9375rem;
  opacity: 0.3;
}
.empty-text[data-v-f4134a5b] {
  font-size: 0.9375rem;
  color: var(--text-gray);
}

/* 下拉刷新 */
.refresh-control[data-v-f4134a5b] {
  position: absolute;
  top: -2.5rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
}
