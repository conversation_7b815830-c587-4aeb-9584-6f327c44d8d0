
/* 容器 */
.all-tasks-container[data-v-4efbdb44] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
}

/* 状态栏占位 - 不再需要 */
.status-bar[data-v-4efbdb44] {
  display: none;
}

/* 页面头部 */
.header[data-v-4efbdb44] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}
.header-left[data-v-4efbdb44] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-4efbdb44] {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-4efbdb44] {
  font-size: 0.875rem;
}
.header-title[data-v-4efbdb44] {
  font-size: 1.125rem;
  font-weight: bold;
}
.date-selector[data-v-4efbdb44] {
  font-size: 1.125rem;
}

/* 日期筛选 */
.date-filter[data-v-4efbdb44] {
  background-color: var(--bg-white);
  padding: 0.625rem 0.9375rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.date-display[data-v-4efbdb44] {
  text-align: center;
}
.date-text[data-v-4efbdb44] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
}

/* 任务统计 */
.task-stats[data-v-4efbdb44] {
  display: flex;
  background-color: var(--bg-white);
  padding: 0.9375rem;
  margin: 0.625rem 0;
}
.stat-item[data-v-4efbdb44] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value[data-v-4efbdb44] {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.3125rem;
}
.stat-label[data-v-4efbdb44] {
  font-size: 0.875rem;
  color: var(--text-gray);
}

/* 任务列表 */
.task-list[data-v-4efbdb44] {
  flex: 1;
}
.task-item[data-v-4efbdb44] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
  padding: 0.625rem;
}
.task-header[data-v-4efbdb44] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.task-id[data-v-4efbdb44] {
  font-size: 0.875rem;
  font-weight: bold;
  color: var(--text-dark);
}
.task-status[data-v-4efbdb44] {
  font-size: 0.75rem;
  padding: 0.1875rem 0.375rem;
  border-radius: 0.1875rem;
  background-color: var(--bg-gray);
  color: var(--text-white);
}
.task-status.status-pending[data-v-4efbdb44] {
  background-color: #f39c12;
}
.task-status.status-completed[data-v-4efbdb44] {
  background-color: #27ae60;
}
.task-content[data-v-4efbdb44] {
  margin-bottom: 0.625rem;
}
.label[data-v-4efbdb44] {
  font-size: 0.75rem;
  color: var(--text-gray);
  margin-right: 0.3125rem;
}
.address[data-v-4efbdb44], .time[data-v-4efbdb44], .customer[data-v-4efbdb44] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  margin-bottom: 0.3125rem;
  display: block;
}
.task-footer[data-v-4efbdb44] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.distance[data-v-4efbdb44] {
  font-size: 0.75rem;
  color: var(--primary-color);
}
.action-btn[data-v-4efbdb44] {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.1875rem;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}
.action-btn.action-pickup[data-v-4efbdb44] {
  background-color: var(--accent-color);
}
.action-btn.action-delivery[data-v-4efbdb44] {
  background-color: #3498db;
}
.action-btn.action-completed[data-v-4efbdb44] {
  background-color: var(--bg-gray);
}

/* 空状态 */
.empty-state[data-v-4efbdb44] {
  text-align: center;
  padding: 3.125rem 0;
}
.empty-text[data-v-4efbdb44] {
  font-size: 0.875rem;
  color: var(--text-gray);
}
