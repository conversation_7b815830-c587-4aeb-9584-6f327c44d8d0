
/* 任务容器 */
.task-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
  position: relative;
}

/* 悬浮头部区域 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--bg-white);
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.1);
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}
.header-title {
  font-size: 1.125rem;
  font-weight: bold;
}



/* 状态标签 */
.status-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 0.875rem;
  color: var(--text-gray);
}
.tab-item.active {
  color: var(--primary-color);
  border-bottom: 0.125rem solid var(--primary-color);
}

/* 任务列表 */
.task-list {
  flex: 1;
  margin-top: 6.25rem;  /* 为固定头部留出空间 */
  padding-top: 0.625rem;
}

/* 筛选与排序 */
.filter-sort-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.filter-options {
  flex: 1;
}
.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3125rem 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
  font-size: 0.8125rem;
}
.arrow {
  margin-left: 0.3125rem;
  font-size: 0.625rem;
}
.search-box {
  display: flex;
  align-items: center;
  margin-left: 0.625rem;
  padding: 0.375rem 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  border: 0.03125rem solid transparent;
}
.search-box:active {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}
.search-icon {
  margin-right: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-gray);
}
.search-placeholder {
  font-size: 0.875rem;
  color: var(--text-gray);
}
.task-item {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
  padding: 0.625rem;
}
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.task-id {
  font-size: 0.875rem;
  font-weight: bold;
  color: var(--text-dark);
}
.task-status {
  font-size: 0.75rem;
  padding: 0.1875rem 0.375rem;
  border-radius: 0.1875rem;
  background-color: var(--bg-gray);
  color: var(--text-white);
}
.task-status.status-pending {
  background-color: #f39c12;
}
.task-status.status-completed {
  background-color: #27ae60;
}
.task-status.status-cancelled {
  background-color: #95a5a6;
}
.task-content {
  margin-bottom: 0.625rem;
}
.label {
  font-size: 0.75rem;
  color: var(--text-gray);
  margin-right: 0.3125rem;
}
.address, .time, .customer {
  font-size: 0.8125rem;
  color: var(--text-dark);
  margin-bottom: 0.3125rem;
  display: block;
}
.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.distance {
  font-size: 0.75rem;
  color: var(--primary-color);
}
.action-btn {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.1875rem;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}
.action-btn.action-pickup {
  background-color: var(--accent-color);
}
.action-btn.action-delivery {
  background-color: #3498db;
}
.action-btn.action-completed {
  background-color: var(--bg-gray);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3.125rem 0;
}
.empty-text {
  font-size: 0.875rem;
  color: var(--text-gray);
}

/* 批量管理 */
.batch-management {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border-top: 0.03125rem solid var(--border-color);
  padding: 0.625rem 0.9375rem;
  padding-bottom: calc(0.625rem + env(safe-area-inset-bottom));
}
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.selectAll {
  display: flex;
  align-items: center;
}
.selectAll uni-text {
  margin-left: 0.3125rem;
  font-size: 0.875rem;
}
.action-buttons {
  display: flex;
  gap: 0.625rem;
}
.batch-btn {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.1875rem;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}
.batch-btn.delete {
  background-color: var(--text-error);
}

/* 管理按钮 */
.management-btn {
  position: fixed;
  bottom: calc(3.75rem + env(safe-area-inset-bottom));
  right: 0.9375rem;
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-primary);
}
