
/* 容器 */
.exclusive-container[data-v-922ee060] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header[data-v-922ee060] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background-color: var(--primary-color);
  color: var(--text-white);
}
.header-left[data-v-922ee060] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-922ee060] {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-922ee060] {
  font-size: 0.875rem;
}
.header-title[data-v-922ee060] {
  font-size: 1.125rem;
  font-weight: bold;
}
.header-right[data-v-922ee060] {
  width: 2.5rem;
}

/* 功能介绍 */
.intro-section[data-v-922ee060] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
  text-align: center;
}
.intro-title[data-v-922ee060] {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.625rem;
}
.intro-text[data-v-922ee060] {
  font-size: 0.8125rem;
  color: var(--text-gray);
  line-height: 1.5;
}

/* 服务类型 */
.service-types[data-v-922ee060] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.section-title[data-v-922ee060] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.type-list[data-v-922ee060] {
  display: flex;
  gap: 0.625rem;
}
.type-item[data-v-922ee060] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.9375rem 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
}
.type-item.active[data-v-922ee060] {
  background-color: var(--primary-color);
  color: var(--text-white);
}
.type-icon[data-v-922ee060] {
  font-size: 1.5rem;
  margin-bottom: 0.46875rem;
}
.type-name[data-v-922ee060] {
  font-size: 0.8125rem;
}

/* 客户信息和取寄信息 */
.customer-info[data-v-922ee060],
.pickup-delivery-info[data-v-922ee060],
.remark-section[data-v-922ee060] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.form-group[data-v-922ee060] {
  margin-bottom: 0.9375rem;
}
.form-group[data-v-922ee060]:last-child {
  margin-bottom: 0;
}
.form-row[data-v-922ee060] {
  display: flex;
  gap: 0.625rem;
}
.half[data-v-922ee060] {
  flex: 1;
}
.form-label[data-v-922ee060] {
  display: block;
  font-size: 0.8125rem;
  color: var(--text-dark);
  margin-bottom: 0.46875rem;
}
.form-input[data-v-922ee060] {
  width: 100%;
  height: 2.1875rem;
  padding: 0 0.625rem;
  font-size: 0.8125rem;
  border: 0.03125rem solid var(--border-color);
  border-radius: 0.3125rem;
  box-sizing: border-box;
}
.form-textarea[data-v-922ee060] {
  width: 100%;
  height: 3.75rem;
  padding: 0.625rem;
  font-size: 0.8125rem;
  border: 0.03125rem solid var(--border-color);
  border-radius: 0.3125rem;
  box-sizing: border-box;
}
.location-btn[data-v-922ee060] {
  width: 100%;
  height: 2.1875rem;
  background-color: var(--bg-light);
  color: var(--text-dark);
  font-size: 0.8125rem;
  border-radius: 0.3125rem;
  border: 0.03125rem solid var(--border-color);
  margin-top: 0.625rem;
}

/* 提交按钮 */
.submit-section[data-v-922ee060] {
  padding: 0.625rem 0.9375rem;
}
.submit-btn[data-v-922ee060] {
  width: 100%;
  height: 2.5rem;
  background-color: var(--primary-color);
  color: var(--text-white);
  font-size: 0.9375rem;
  border-radius: 0.3125rem;
  border: none;
}
