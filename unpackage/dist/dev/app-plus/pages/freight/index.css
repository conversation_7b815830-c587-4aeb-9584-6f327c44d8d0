
/* 容器 */
.freight-container[data-v-3f412751] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header[data-v-3f412751] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.5rem 0.9375rem 0.625rem 0.9375rem;  /* 固定的顶部间距 */
  background: linear-gradient(135deg, #1e6eeb, #0d5cb6);
  color: var(--text-white);
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
}
.header-left[data-v-3f412751] {
  display: flex;
  align-items: center;
  padding: 0.3125rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.header-left[data-v-3f412751]:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.2);
}
.back-icon[data-v-3f412751] {
  font-size: 1.125rem;
  margin-right: 0.25rem;
  color: white;
}
.back-text[data-v-3f412751] {
  font-size: 0.875rem;
  color: white;
}
.header-title[data-v-3f412751] {
  font-size: 1.125rem;
  font-weight: bold;
}
.header-right[data-v-3f412751] {
  width: 2.5rem;
}

/* 查询表单 */
.query-form[data-v-3f412751] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.form-group[data-v-3f412751] {
  margin-bottom: 0.9375rem;
}
.form-row[data-v-3f412751] {
  display: flex;
  gap: 0.625rem;
}
.half[data-v-3f412751] {
  flex: 1;
}
.form-label[data-v-3f412751] {
  display: block;
  font-size: 0.8125rem;
  color: var(--text-dark);
  margin-bottom: 0.46875rem;
}
.location-selector[data-v-3f412751] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
}
.location-text[data-v-3f412751] {
  font-size: 0.8125rem;
  color: var(--text-dark);
}
.arrow[data-v-3f412751] {
  font-size: 0.625rem;
  color: var(--text-gray);
}
.form-input[data-v-3f412751] {
  width: 100%;
  height: 2.1875rem;
  padding: 0 0.625rem;
  font-size: 0.8125rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.3125rem;
  box-sizing: border-box;
  background-color: #fff;
  transition: all 0.3s ease;
}
.form-input[data-v-3f412751]:focus {
  border-color: #1e6eeb;
  box-shadow: 0 0 0.625rem rgba(30, 110, 235, 0.2);
  outline: none;
}
.type-selector[data-v-3f412751] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}
.type-item[data-v-3f412751] {
  padding: 0.46875rem 0.78125rem;
  font-size: 0.8125rem;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
  color: var(--text-dark);
}
.type-item.active[data-v-3f412751] {
  background-color: var(--primary-color);
  color: var(--text-white);
}
.query-btn[data-v-3f412751] {
  width: 100%;
  height: 2.5rem;
  background: linear-gradient(135deg, #1e6eeb, #0d5cb6);
  color: var(--text-white);
  font-size: 0.9375rem;
  border-radius: 0.3125rem;
  border: none;
  margin-top: 0.625rem;
  box-shadow: 0 0.125rem 0.375rem rgba(30, 110, 235, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}
.query-btn[data-v-3f412751]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.0625rem 0.25rem rgba(30, 110, 235, 0.4);
}

/* 查询结果 */
.result-section[data-v-3f412751] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.section-title[data-v-3f412751] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.fee-item[data-v-3f412751] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
}
.fee-label[data-v-3f412751] {
  font-size: 0.8125rem;
  color: var(--text-dark);
}
.fee-value[data-v-3f412751] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  font-weight: 500;
}
.fee-divider[data-v-3f412751] {
  height: 0.03125rem;
  background-color: var(--border-color);
  margin: 0.625rem 0;
}
.total[data-v-3f412751] {
  font-weight: bold;
}
.total-value[data-v-3f412751] {
  color: #e74c3c;
  font-size: 1rem;
}

/* 运费说明 */
.freight-info[data-v-3f412751] {
  background-color: var(--bg-white);
  margin: 0.625rem 0.9375rem;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.info-title[data-v-3f412751] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid var(--border-color);
}
.info-content[data-v-3f412751] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
}
.info-text[data-v-3f412751] {
  font-size: 0.75rem;
  color: var(--text-gray);
  line-height: 1.5;
}
