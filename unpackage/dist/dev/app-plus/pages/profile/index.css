
/* 个人中心页面样式 */

/* 个人中心容器 */
.profile-container[data-v-201c0da5] {
  height: 100vh;
  background: var(--bg-light);
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 悬浮用户信息头部 */
.fixed-profile-header[data-v-201c0da5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.1);
}

/* 用户信息头部 */
.profile-header[data-v-201c0da5] {
  position: relative;
  padding: 2.5rem 1.25rem 1.25rem;  /* 固定的顶部间距 */
  overflow: hidden;
}
.background-gradient[data-v-201c0da5] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #5dade2 100%);
}
.user-card[data-v-201c0da5] {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-large);
  padding: 0.78125rem;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  box-shadow: var(--shadow-medium);
}

/* 用户头像区域 */
.user-avatar-section[data-v-201c0da5] {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 0.625rem;
}
.avatar-container[data-v-201c0da5] {
  position: relative;
  width: 3.125rem;
  height: 3.125rem;
}
.avatar[data-v-201c0da5] {
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  border: 0.125rem solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 0.1875rem 0.5625rem rgba(0,0,0,0.15);
}
.avatar-ring[data-v-201c0da5] {
  position: absolute;
  top: -0.25rem;
  left: -0.25rem;
  width: 4.875rem;
  height: 4.875rem;
  border: 0.09375rem solid var(--primary-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-ring-201c0da5 2s infinite;
}
.status-indicator[data-v-201c0da5] {
  position: absolute;
  bottom: 0.3125rem;
  right: 0.3125rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 0.09375rem solid white;
}
.status-indicator.online[data-v-201c0da5] {
  background: #4CAF50;
}
@keyframes pulse-ring-201c0da5 {
0% {
    transform: scale(0.95);
    opacity: 0.3;
}
50% {
    transform: scale(1.05);
    opacity: 0.1;
}
100% {
    transform: scale(0.95);
    opacity: 0.3;
}
}

/* 用户信息 */
.user-info[data-v-201c0da5] {
  text-align: center;
  margin-bottom: 0.625rem;
  flex: 1;
}
.user-name[data-v-201c0da5] {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.375rem;
}
.user-title-container[data-v-201c0da5] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.46875rem;
  margin-bottom: 0.625rem;
}
.user-title[data-v-201c0da5] {
  font-size: 0.875rem;
  color: var(--primary-color);
  font-weight: 500;
}
.vip-badge[data-v-201c0da5] {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.625rem;
  font-weight: bold;
  box-shadow: 0 0.0625rem 0.25rem rgba(255, 165, 0, 0.3);
}
.vip-text[data-v-201c0da5] {
  font-size: 0.625rem;
  font-weight: bold;
}
.user-meta[data-v-201c0da5] {
  display: flex;
  justify-content: center;
  gap: 0.78125rem;
  margin-bottom: 0.625rem;
}
.meta-item[data-v-201c0da5] {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.meta-icon[data-v-201c0da5] {
  font-size: 0.625rem;
}
.meta-text[data-v-201c0da5] {
  font-size: 0.75rem;
  color: var(--text-gray);
}

/* 头部操作按钮 */
.header-actions[data-v-201c0da5] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
}
.action-btn[data-v-201c0da5] {
  width: 1.5625rem;
  height: 1.5625rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.125rem 0.375rem rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}
.action-btn[data-v-201c0da5]:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}
.action-icon[data-v-201c0da5] {
  font-size: 0.75rem;
}

/* 页面内容区域 */
.profile-content[data-v-201c0da5] {
  margin-top: 6.25rem;  /* 为固定头部留出空间 */
  padding-top: 0.625rem;
}

/* 统计区域 */
.stats-section[data-v-201c0da5] {
  margin-bottom: 0.625rem;
  background: var(--bg-white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}
.section-header[data-v-201c0da5] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem 1.25rem 0.625rem;
}
.section-title[data-v-201c0da5] {
  flex: 1;
}
.title-text[data-v-201c0da5] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.15625rem;
}
.title-desc[data-v-201c0da5] {
  font-size: 0.75rem;
  color: var(--text-gray);
}
.view-more[data-v-201c0da5] {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--bg-light);
  border-radius: 0.625rem;
  transition: all 0.3s ease;
}
.view-more[data-v-201c0da5]:active {
  background: var(--bg-gray);
}
.more-text[data-v-201c0da5] {
  font-size: 0.75rem;
  color: var(--primary-color);
}
.more-arrow[data-v-201c0da5] {
  font-size: 0.625rem;
  color: var(--primary-color);
}
.stats-container[data-v-201c0da5] {
  padding: 0 1.25rem 0.9375rem;
}
.primary-stats[data-v-201c0da5] {
  margin-bottom: 0.9375rem;
}
.primary-stat-card[data-v-201c0da5] {
  background: linear-gradient(135deg, var(--primary-color), #5dade2);
  border-radius: var(--border-radius-large);
  padding: 0.9375rem;
  color: white;
  position: relative;
  overflow: hidden;
}
.primary-stat-card[data-v-201c0da5]::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}
.stat-header[data-v-201c0da5] {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.625rem;
}
.stat-value.primary[data-v-201c0da5] {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.46875rem;
}
.stat-footer[data-v-201c0da5] {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}
.stat-trend.positive[data-v-201c0da5] {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  font-weight: bold;
}
.stat-desc[data-v-201c0da5] {
  font-size: 0.6875rem;
  opacity: 0.8;
}
.secondary-stats[data-v-201c0da5] {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.46875rem;
}
.stat-card.compact[data-v-201c0da5] {
  background: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 0.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  border: 0.03125rem solid transparent;
}
.stat-card.compact[data-v-201c0da5]:active {
  transform: scale(0.98);
  background: var(--bg-gray);
  border-color: var(--primary-color);
}
.stat-card.compact .stat-icon[data-v-201c0da5] {
  font-size: 1rem;
  margin-bottom: 0.375rem;
}
.stat-content[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.25rem;
}
.stat-card.compact .stat-value[data-v-201c0da5] {
  font-size: 0.875rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.125rem;
}
.stat-card.compact .stat-label[data-v-201c0da5] {
  font-size: 0.625rem;
  color: var(--text-gray);
}
.stat-card.compact .stat-trend[data-v-201c0da5] {
  font-size: 0.5625rem;
  color: var(--success-color);
  font-weight: bold;
  background: rgba(76, 175, 80, 0.1);
  padding: 0.0625rem 0.25rem;
  border-radius: 0.25rem;
}

/* 快捷功能 */
.quick-actions[data-v-201c0da5] {
  margin-bottom: 0.625rem;
  background: var(--bg-white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}
.action-grid[data-v-201c0da5] {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.625rem;
  padding: 0 1.25rem 0.9375rem;
}
.action-item.modern[data-v-201c0da5] {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.9375rem 0.625rem;
  border-radius: var(--border-radius-large);
  transition: all 0.3s ease;
  background: var(--bg-light);
  border: 0.03125rem solid transparent;
  position: relative;
  overflow: hidden;
}
.action-item.modern[data-v-201c0da5]:active {
  transform: scale(0.98);
  border-color: var(--primary-color);
  background: rgba(52, 152, 219, 0.05);
}
.action-icon-container[data-v-201c0da5] {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.46875rem;
  position: relative;
}
.action-icon-container.schedule[data-v-201c0da5] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.action-icon-container.area[data-v-201c0da5] {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.action-icon-container.training[data-v-201c0da5] {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.action-icon-container.feedback[data-v-201c0da5] {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.action-icon-container .action-icon[data-v-201c0da5] {
  font-size: 1.125rem;
  filter: brightness(0) invert(1);
}
.action-text[data-v-201c0da5] {
  font-size: 0.8125rem;
  color: var(--text-dark);
  font-weight: 500;
  margin-bottom: 0.15625rem;
}
.action-desc[data-v-201c0da5] {
  font-size: 0.6875rem;
  color: var(--text-gray);
  text-align: center;
}

/* 设置菜单 */
.settings-section[data-v-201c0da5] {
  margin-bottom: 0.9375rem;
  background: var(--bg-white);
}
.menu-group[data-v-201c0da5] {
  padding: 0 1.25rem 0.625rem;
}
.menu-item[data-v-201c0da5] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem 0;
  border-bottom: 0.03125rem solid var(--border-color);
}
.menu-item[data-v-201c0da5]:last-child {
  border-bottom: none;
}
.menu-left[data-v-201c0da5] {
  display: flex;
  align-items: center;
  flex: 1;
}
.menu-icon[data-v-201c0da5] {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
  font-size: 1rem;
}
.menu-icon.wallet[data-v-201c0da5] {
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
}
.menu-icon.orders[data-v-201c0da5] {
  background: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
}
.menu-icon.certificates[data-v-201c0da5] {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}
.menu-icon.settings[data-v-201c0da5] {
  background: rgba(241, 196, 15, 0.1);
  color: var(--warning-color);
}
.menu-icon.help[data-v-201c0da5] {
  background: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}
.menu-icon.about[data-v-201c0da5] {
  background: rgba(149, 165, 166, 0.1);
  color: var(--text-gray);
}
.menu-content[data-v-201c0da5] {
  display: flex;
  flex-direction: column;
}
.menu-title[data-v-201c0da5] {
  font-size: 0.9375rem;
  color: var(--text-dark);
  margin-bottom: 0.15625rem;
}
.menu-desc[data-v-201c0da5] {
  font-size: 0.75rem;
  color: var(--text-gray);
}
.menu-right[data-v-201c0da5] {
  display: flex;
  align-items: center;
}
.menu-value[data-v-201c0da5] {
  font-size: 0.875rem;
  color: var(--text-dark);
  margin-right: 0.46875rem;
  font-weight: bold;
}
.menu-arrow[data-v-201c0da5] {
  font-size: 1rem;
  color: var(--text-light);
}

/* 退出登录 */
.logout-section[data-v-201c0da5] {
  padding: 0 1.25rem 1.25rem;
}
.logout-btn[data-v-201c0da5] {
  background: var(--bg-white);
  border: 0.03125rem solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.logout-btn[data-v-201c0da5]:active {
  background: var(--bg-light);
  transform: scale(0.98);
}
.logout-icon[data-v-201c0da5] {
  font-size: 1.125rem;
  margin-right: 0.46875rem;
}
.logout-text[data-v-201c0da5] {
  font-size: 1rem;
  color: var(--danger-color);
  font-weight: bold;
}

