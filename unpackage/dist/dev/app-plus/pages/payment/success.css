
/* CSS变量 */
[data-v-cbafefc3]:root {
  --primary-color: #3498db;
  --success-color: #27ae60;
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --text-dark: #2c3e50;
  --text-gray: #7f8c8d;
  --border-color: #dcdde1;
  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
  --border-radius: 0.375rem;
}

/* 页面容器 */
.payment-success-container[data-v-cbafefc3] {
  min-height: 100vh;
  background: var(--bg-light);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 状态栏占位 */
.status-bar[data-v-cbafefc3] {
  height: env(safe-area-inset-top);
  background: var(--primary-color);
}

/* 页面头部 */
.header[data-v-cbafefc3] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.9375rem 1.25rem;
  background: var(--primary-color);
  color: white;
}
.header-left[data-v-cbafefc3] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-cbafefc3] {
  font-size: 1.125rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-cbafefc3] {
  font-size: 0.875rem;
}
.header-title[data-v-cbafefc3] {
  font-size: 1rem;
  font-weight: bold;
}

/* 成功状态 */
.success-section[data-v-cbafefc3] {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2.5rem 1.25rem;
  background: var(--bg-white);
  margin-bottom: 0.625rem;
}
.success-icon-container[data-v-cbafefc3] {
  position: relative;
  margin-bottom: 1.25rem;
}
.success-icon[data-v-cbafefc3] {
  font-size: 3.75rem;
  color: var(--success-color);
}
.success-animation[data-v-cbafefc3] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6.25rem;
  height: 6.25rem;
  border: 0.125rem solid var(--success-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-cbafefc3 2s infinite;
}
@keyframes pulse-cbafefc3 {
0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
}
50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
}
100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
}
}
.success-title[data-v-cbafefc3] {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--success-color);
  margin-bottom: 0.625rem;
}
.success-desc[data-v-cbafefc3] {
  font-size: 0.875rem;
  color: var(--text-gray);
}

/* 支付信息 */
.payment-info[data-v-cbafefc3] {
  background: var(--bg-white);
  margin-bottom: 0.625rem;
  padding: 1.25rem;
}
.info-item[data-v-cbafefc3] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.78125rem 0;
  border-bottom: 1px solid var(--border-color);
}
.info-item[data-v-cbafefc3]:last-child {
  border-bottom: none;
}
.label[data-v-cbafefc3] {
  font-size: 0.875rem;
  color: var(--text-gray);
}
.value[data-v-cbafefc3] {
  font-size: 0.875rem;
  color: var(--text-dark);
  font-weight: bold;
}
.amount[data-v-cbafefc3] {
  font-size: 1.125rem;
  color: var(--success-color);
}

/* 操作按钮 */
.action-section[data-v-cbafefc3] {
  padding: 1.25rem;
  background: var(--bg-white);
  margin-bottom: 0.625rem;
}
.action-btn[data-v-cbafefc3] {
  width: 100%;
  height: 2.5rem;
  font-size: 0.9375rem;
  border-radius: var(--border-radius);
  border: none;
  margin-bottom: 0.625rem;
  transition: all 0.3s ease;
}
.action-btn[data-v-cbafefc3]:last-child {
  margin-bottom: 0;
}
.primary-btn[data-v-cbafefc3] {
  background: var(--primary-color);
  color: white;
}
.secondary-btn[data-v-cbafefc3] {
  background: var(--bg-light);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}
.action-btn[data-v-cbafefc3]:active {
  transform: scale(0.98);
}

/* 温馨提示 */
.tips-section[data-v-cbafefc3] {
  background: var(--bg-white);
  padding: 1.25rem;
}
.tips-title[data-v-cbafefc3] {
  font-size: 0.875rem;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 0.625rem;
}
.tips-text[data-v-cbafefc3] {
  display: block;
  font-size: 0.75rem;
  color: var(--text-gray);
  line-height: 1.6;
  margin-bottom: 0.3125rem;
}
