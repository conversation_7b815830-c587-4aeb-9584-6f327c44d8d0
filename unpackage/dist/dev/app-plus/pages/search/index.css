
/* 容器 */
.search-container[data-v-2dab939d] {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header[data-v-2dab939d] {
  display: flex;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: var(--bg-white);
  border-bottom: 0.03125rem solid var(--border-color);
}
.header-left[data-v-2dab939d] {
  display: flex;
  align-items: center;
  margin-right: 0.625rem;
}
.back-icon[data-v-2dab939d] {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-2dab939d] {
  font-size: 0.875rem;
}
.search-box[data-v-2dab939d] {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: var(--bg-light);
  border-radius: 0.3125rem;
  padding: 0.3125rem 0.625rem;
}
.search-icon[data-v-2dab939d] {
  font-size: 0.875rem;
  margin-right: 0.3125rem;
  color: var(--text-gray);
}
.search-input[data-v-2dab939d] {
  flex: 1;
  font-size: 0.875rem;
  padding: 0.3125rem 0;
  background: transparent;
  border: none;
  outline: none;
}
.clear-icon[data-v-2dab939d] {
  font-size: 0.875rem;
  color: var(--text-gray);
  padding: 0.3125rem;
}
.cancel-btn[data-v-2dab939d] {
  font-size: 0.875rem;
  color: var(--primary-color);
  margin-left: 0.625rem;
  padding: 0.3125rem 0;
}

/* 搜索结果 */
.search-results[data-v-2dab939d] {
  flex: 1;
  padding: 0.625rem 0.9375rem;
}
.result-item[data-v-2dab939d] {
  background-color: var(--bg-white);
  border-radius: 0.3125rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.result-header[data-v-2dab939d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.result-title[data-v-2dab939d] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: var(--text-dark);
}
.result-type[data-v-2dab939d] {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.1875rem;
  color: var(--text-white);
}
.result-type.pickup[data-v-2dab939d] {
  background-color: var(--accent-color);
}
.result-type.delivery[data-v-2dab939d] {
  background-color: #3498db;
}
.result-content[data-v-2dab939d] {
  margin-bottom: 0.3125rem;
}
.result-desc[data-v-2dab939d] {
  font-size: 0.8125rem;
  color: var(--text-gray);
}
.result-meta[data-v-2dab939d] {
  text-align: right;
}
.result-time[data-v-2dab939d] {
  font-size: 0.75rem;
  color: var(--text-gray);
}

/* 最近搜索 */
.recent-searches[data-v-2dab939d] {
  flex: 1;
  padding: 0.625rem 0.9375rem;
}
.section-header[data-v-2dab939d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.9375rem 0 0.625rem;
}
.section-title[data-v-2dab939d] {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-dark);
}
.clear-all[data-v-2dab939d] {
  font-size: 0.8125rem;
  color: var(--primary-color);
}
.history-list[data-v-2dab939d] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}
.history-item[data-v-2dab939d] {
  background-color: var(--bg-white);
  padding: 0.46875rem 0.78125rem;
  border-radius: 0.3125rem;
  font-size: 0.8125rem;
  color: var(--text-dark);
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
.hot-searches[data-v-2dab939d] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}
.hot-item[data-v-2dab939d] {
  background-color: var(--bg-white);
  padding: 0.46875rem 0.78125rem;
  border-radius: 0.3125rem;
  font-size: 0.8125rem;
  color: var(--primary-color);
  box-shadow: 0 0.125rem 0.375rem var(--shadow-light);
}
